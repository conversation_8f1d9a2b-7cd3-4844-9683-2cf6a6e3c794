#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场数据获取示例
演示如何使用 market_info.py 模块获取股票市场数据
"""

from market_info import MarketDataCollector, format_market_data, save_to_csv
import json

def main():
    """主函数，演示各种数据获取功能"""
    
    # 创建市场数据收集器
    collector = MarketDataCollector()
    
    print("=== 股票市场数据获取示例 ===\n")
    
    # 1. 获取市场情绪数据
    print("1. 获取市场情绪数据...")
    sentiment = collector.get_market_sentiment()
    if sentiment:
        print(f"   总股票数: {sentiment['total_stocks']}只")
        print(f"   上涨家数: {sentiment['up_count']}只 ({sentiment['up_ratio']}%)")
        print(f"   下跌家数: {sentiment['down_count']}只 ({sentiment['down_ratio']}%)")
        print(f"   平盘家数: {sentiment['flat_count']}只")
        print(f"   总成交量: {sentiment['total_volume']:.0f}手")
        print(f"   总成交额: {sentiment['total_amount']:.2e}元")
    print()
    
    # 2. 获取涨跌停统计
    print("2. 获取涨跌停统计...")
    limit_stats = collector.get_limit_up_down_stats()
    if limit_stats:
        print(f"   涨停家数: {limit_stats['limit_up_count']}只")
        print(f"   跌停家数: {limit_stats['limit_down_count']}只")
        print(f"   昨日涨停家数: {limit_stats['yesterday_limit_up_count']}只")

        # 显示部分涨停股
        if limit_stats.get('limit_up_details'):
            print("   今日涨停股(前5只):")
            for i, stock in enumerate(limit_stats['limit_up_details'][:5], 1):
                print(f"     {i}. {stock.get('名称', 'N/A')} ({stock.get('代码', 'N/A')}) "
                      f"涨幅: {stock.get('涨跌幅', 'N/A')}%")

    # 从市场情绪数据中也可以获取涨跌停统计
    if sentiment:
        print(f"   从实时行情统计:")
        print(f"   涨停家数: {sentiment.get('limit_up_count', 'N/A')}只")
        print(f"   跌停家数: {sentiment.get('limit_down_count', 'N/A')}只")
        print(f"   大涨家数(≥5%): {sentiment.get('big_up_count', 'N/A')}只")
        print(f"   大跌家数(≤-5%): {sentiment.get('big_down_count', 'N/A')}只")
        print(f"   数据来源: {sentiment.get('data_source', 'N/A')}")
    print()
    
    # 3. 获取市场总貌
    print("3. 获取市场总貌...")
    summary = collector.get_market_summary()
    if summary:
        # 上交所数据
        if summary.get('sse_summary'):
            print("   上海证券交易所:")
            for item in summary['sse_summary']:
                if '股票' in str(item.get('项目', '')):
                    print(f"     {item.get('项目', 'N/A')}: {item.get('股票', 'N/A')}")
        
        # 深交所数据
        if summary.get('szse_summary'):
            print("   深圳证券交易所:")
            for item in summary['szse_summary'][:3]:  # 只显示前3项
                print(f"     {item.get('证券类别', 'N/A')}: "
                      f"数量 {item.get('数量', 'N/A')}只, "
                      f"成交金额 {item.get('成交金额', 0):.2e}元")
    print()
    
    # 4. 获取综合数据并保存
    print("4. 获取综合数据并保存...")
    comprehensive_data = collector.get_comprehensive_market_data()
    if comprehensive_data:
        # 保存为JSON
        with open('market_data_example.json', 'w', encoding='utf-8') as f:
            json.dump(comprehensive_data, f, ensure_ascii=False, indent=2)
        
        # 保存为CSV
        save_to_csv(comprehensive_data, 'market_data_example.csv')
        
        print("   数据已保存到 market_data_example.json 和 market_data_example.csv")
    
    print("\n=== 数据获取完成 ===")

if __name__ == "__main__":
    main()
