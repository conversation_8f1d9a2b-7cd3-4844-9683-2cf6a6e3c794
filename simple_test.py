#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试AKShare连接
"""

import akshare as ak
import time

def test_akshare_connection():
    """测试AKShare连接"""
    
    print("=== 测试AKShare连接 ===\n")
    
    # 测试1: 获取股票基本信息
    print("1. 测试股票基本信息...")
    try:
        stock_info = ak.stock_info_a_code_name()
        if not stock_info.empty:
            print(f"   成功获取 {len(stock_info)} 只股票信息")
            print(f"   示例: {stock_info.head(3).to_dict('records')}")
        else:
            print("   股票信息为空")
    except Exception as e:
        print(f"   获取股票信息失败: {e}")
    
    print()
    
    # 测试2: 获取指数数据
    print("2. 测试指数数据...")
    try:
        index_data = ak.stock_zh_index_spot()
        if not index_data.empty:
            print(f"   成功获取 {len(index_data)} 个指数数据")
            # 查找上证指数和深证成指
            for _, row in index_data.iterrows():
                if '上证指数' in str(row.get('名称', '')) or '000001' in str(row.get('代码', '')):
                    print(f"   上证指数: {row.get('最新价', 'N/A')} 涨跌幅: {row.get('涨跌幅', 'N/A')}%")
                elif '深证成指' in str(row.get('名称', '')) or '399001' in str(row.get('代码', '')):
                    print(f"   深证成指: {row.get('最新价', 'N/A')} 涨跌幅: {row.get('涨跌幅', 'N/A')}%")
        else:
            print("   指数数据为空")
    except Exception as e:
        print(f"   获取指数数据失败: {e}")
    
    print()
    
    # 测试3: 获取市场总貌（上交所）
    print("3. 测试上交所市场总貌...")
    try:
        sse_summary = ak.stock_sse_summary()
        if not sse_summary.empty:
            print(f"   成功获取上交所数据: {len(sse_summary)} 项")
            for _, row in sse_summary.iterrows():
                if '股票' in str(row.get('项目', '')):
                    print(f"   {row.get('项目', 'N/A')}: {row.get('股票', 'N/A')}")
        else:
            print("   上交所数据为空")
    except Exception as e:
        print(f"   获取上交所数据失败: {e}")
    
    print()
    
    # 测试4: 获取深交所市场总貌
    print("4. 测试深交所市场总貌...")
    try:
        from datetime import datetime, timedelta
        # 尝试获取最近几天的数据
        for i in range(5):
            date = (datetime.now() - timedelta(days=i)).strftime('%Y%m%d')
            try:
                szse_summary = ak.stock_szse_summary(date=date)
                if not szse_summary.empty:
                    print(f"   成功获取深交所数据({date}): {len(szse_summary)} 项")
                    for _, row in szse_summary.head(3).iterrows():
                        print(f"   {row.get('证券类别', 'N/A')}: 数量 {row.get('数量', 'N/A')}只")
                    break
            except:
                continue
        else:
            print("   未能获取深交所数据")
    except Exception as e:
        print(f"   获取深交所数据失败: {e}")
    
    print()
    
    # 测试5: 模拟涨跌停数据
    print("5. 模拟涨跌停统计...")
    # 由于网络问题，我们创建一些模拟数据来演示功能
    import pandas as pd
    import numpy as np
    
    # 创建模拟股票数据
    np.random.seed(42)
    n_stocks = 5000
    
    # 生成随机涨跌幅数据
    changes = np.random.normal(0, 3, n_stocks)  # 平均0%，标准差3%的正态分布
    
    # 添加一些涨停和跌停
    limit_up_indices = np.random.choice(n_stocks, 50, replace=False)
    limit_down_indices = np.random.choice(n_stocks, 20, replace=False)
    
    changes[limit_up_indices] = 10.0  # 涨停
    changes[limit_down_indices] = -10.0  # 跌停
    
    # 统计
    up_count = len(changes[changes > 0])
    down_count = len(changes[changes < 0])
    flat_count = len(changes[changes == 0])
    limit_up_count = len(changes[changes >= 9.9])
    limit_down_count = len(changes[changes <= -9.9])
    big_up_count = len(changes[changes >= 5])
    big_down_count = len(changes[changes <= -5])
    
    print(f"   模拟数据统计:")
    print(f"   总股票数: {n_stocks}只")
    print(f"   上涨家数: {up_count}只 ({up_count/n_stocks*100:.2f}%)")
    print(f"   下跌家数: {down_count}只 ({down_count/n_stocks*100:.2f}%)")
    print(f"   平盘家数: {flat_count}只 ({flat_count/n_stocks*100:.2f}%)")
    print(f"   涨停家数: {limit_up_count}只 ({limit_up_count/n_stocks*100:.2f}%)")
    print(f"   跌停家数: {limit_down_count}只 ({limit_down_count/n_stocks*100:.2f}%)")
    print(f"   大涨家数(≥5%): {big_up_count}只")
    print(f"   大跌家数(≤-5%): {big_down_count}只")
    print(f"   平均涨跌幅: {np.mean(changes):.2f}%")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_akshare_connection()
