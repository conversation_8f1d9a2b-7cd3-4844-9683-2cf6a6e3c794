#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析被过滤掉的股票，了解多算了哪些股票
"""

from market_info_sina import SinaMarketDataCollector
import json

def main():
    """分析被过滤的股票"""
    
    collector = SinaMarketDataCollector()
    
    print("=== 股票过滤分析 ===\n")
    
    # 1. 获取当前的市场数据
    print("1. 获取当前市场统计...")
    sentiment = collector.get_market_sentiment()
    if sentiment:
        print(f"   当前有效股票数: {sentiment['total_stocks']}只")
        print(f"   同花顺显示: 5217只")
        print(f"   差异: {sentiment['total_stocks'] - 5217}只")
    print()
    
    # 2. 分析被过滤的股票
    print("2. 分析被过滤的股票...")
    analysis = collector.analyze_filtered_stocks()
    
    if analysis:
        print(f"   总获取股票数: {analysis['total_fetched']}只")
        print(f"   有效股票数: {analysis['valid_stocks']}只")
        print(f"   被过滤股票数: {analysis['total_filtered']}只")
        print()
        
        print("   过滤原因分析:")
        print(f"   - 退市股票: {analysis['delisted_count']}只")
        print(f"   - 低价股票: {analysis['low_price_count']}只")
        print(f"   - 无效数据: {analysis['invalid_data_count']}只")
        print(f"   - 其他原因: {analysis['other_filtered_count']}只")
        print()
        
        # 显示退市股票示例
        if analysis['delisted_stocks']:
            print("   退市股票示例(前20只):")
            for i, stock in enumerate(analysis['delisted_stocks'][:20], 1):
                print(f"     {i}. {stock['name']} ({stock['code']}) 价格: {stock['price']}元")
        print()
        
        # 显示低价股票示例
        if analysis['low_price_stocks']:
            print("   低价股票示例(前20只):")
            for i, stock in enumerate(analysis['low_price_stocks'][:20], 1):
                print(f"     {i}. {stock['name']} ({stock['code']}) 价格: {stock['price']}元")
        print()
        
        # 显示无效数据股票示例
        if analysis['invalid_data_stocks']:
            print("   无效数据股票示例(前20只):")
            for i, stock in enumerate(analysis['invalid_data_stocks'][:20], 1):
                print(f"     {i}. {stock['name']} ({stock['code']}) 价格: {stock['price']}元")
        print()
        
        # 保存详细分析结果
        with open('filtered_stocks_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        print("   详细分析结果已保存到 filtered_stocks_analysis.json")
    
    print()
    
    # 3. 建议的改进措施
    print("3. 改进建议:")
    print("   - 已实施退市股票过滤")
    print("   - 已实施低价股票过滤（<0.1元）")
    print("   - 已实施无效数据过滤")
    print("   - 可以进一步调整过滤条件以接近同花顺的5217只")
    
    # 4. 对比分析
    if sentiment and analysis:
        target_count = 5217
        current_count = sentiment['total_stocks']
        difference = current_count - target_count
        
        print(f"\n4. 对比分析:")
        print(f"   目标股票数（同花顺）: {target_count}只")
        print(f"   当前股票数: {current_count}只")
        print(f"   需要进一步过滤: {difference}只")
        
        if difference > 0:
            print(f"\n   建议进一步过滤的候选:")
            print(f"   - B股可能不计入主流统计: 约94只")
            print(f"   - 部分特殊股票代码")
            print(f"   - 可能需要更严格的退市股票识别")

if __name__ == "__main__":
    main()
