#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大板客网站连板雁阵图数据提取脚本 - 成功版本
基于之前成功的方法，使用主页面数据并按级别分类
"""

import requests
import re
from datetime import datetime
import json
from bs4 import BeautifulSoup

def get_dabanke_data(date_str=None):
    """获取大板客数据

    Args:
        date_str: 日期字符串，格式为YYYYMMDD，如果为None则获取当前数据
    """
    if date_str:
        url = f"https://dabanke.com/index-{date_str}.html"
    else:
        url = "https://dabanke.com/"

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        response.encoding = 'utf-8'
        return response.text
    except Exception as e:
        print(f"❌ 获取数据失败 ({url}): {e}")
        return None

def extract_all_stocks_and_classify(html_content):
    """提取所有股票并按级别分类"""
    # 支持从首板到24进25的所有级别
    level_names = ['首板', '1进2', '2进3', '3进4', '4进5', '5进6', '6进7', '7进8', '8进9', '9进10',
                  '10进11', '11进12', '12进13', '13进14', '14进15', '15进16', '16进17', '17进18',
                  '18进19', '19进20', '20进21', '21进22', '22进23', '23进24', '24进25']

    stocks_by_level = {}
    for level in level_names:
        stocks_by_level[level] = []

    data = {
        'update_time': '',
        'lianban_progress': {},
        'stocks_by_level': stocks_by_level,
        'concept_stats': []
    }
    
    # 提取更新时间
    time_match = re.search(r'最后更新时间:\s*(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', html_content)
    if time_match:
        data['update_time'] = time_match.group(1)
    
    # 提取连板进度统计 - 使用更精确的匹配方式
    # 先找到所有的比例数据和它们的上下文
    ratio_pattern = r'(\d+/\d+=\d+%)'
    ratio_matches = list(re.finditer(ratio_pattern, html_content))

    for match in ratio_matches:
        ratio = match.group(1)
        # 获取比例数据前面的文本，查找级别标识
        start = max(0, match.start() - 300)
        context_before = html_content[start:match.start()]

        # 检查各个级别
        level_found = None

        # 检查高级别连板（从24进25到1进2）
        for i in range(24, 0, -1):
            level_name = f'{i}进{i+1}'
            if level_name in context_before[-200:]:  # 检查最近的200个字符
                level_found = level_name
                break

        # 检查首板
        if not level_found and '首板' in context_before[-200:]:
            level_found = '首板'

        # 如果找到了级别，保存数据
        if level_found:
            data['lianban_progress'][level_found] = ratio
            print(f"匹配到 {level_found}: {ratio}")
    
    # 使用BeautifulSoup提取股票信息
    soup = BeautifulSoup(html_content, 'html.parser')
    stock_links = soup.find_all('a', href=re.compile(r'/gupiao-\d+\.html'))
    
    all_stocks = []
    seen_codes = set()
    
    for link in stock_links:
        href = link.get('href', '')
        code_match = re.search(r'/gupiao-(\d+)\.html', href)
        if code_match:
            code = code_match.group(1)
            name = link.get_text().strip()
            
            if name and len(name) > 1 and code not in seen_codes:
                seen_codes.add(code)
                
                # 查找股票周围的信息
                parent_text = ''
                current = link.parent
                for _ in range(5):  # 向上查找5层父元素
                    if current:
                        parent_text += current.get_text() + ' '
                        current = current.parent
                
                # 提取状态
                status_match = re.search(r'\(([成败炸])\)', parent_text)
                status = status_match.group(1) if status_match else ''
                
                # 提取涨跌幅
                percent_match = re.search(r'([-+]?\d+\.?\d*)\s*%', parent_text)
                percent = percent_match.group(1) if percent_match else ''
                
                # 提取行业信息
                industry_parts = parent_text.split()
                industry = ''
                for part in industry_parts:
                    if '概念' in part or '板块' in part or '军工' in part or '新疆' in part:
                        industry = part
                        break
                
                if status or percent:  # 只保存有状态或涨跌幅的股票
                    all_stocks.append({
                        'name': name,
                        'code': code,
                        'status': status,
                        'change_percent': percent,
                        'industry': industry
                    })
    
    print(f"成功提取到 {len(all_stocks)} 只股票")
    
    # 根据连板进度统计动态分配股票到不同级别
    # 首先解析连板进度统计，获取每个级别的股票总数
    level_counts = {}
    level_order = []

    # 按级别从高到低排序（24进25 -> 23进24 -> ... -> 3进4 -> 2进3 -> 1进2 -> 首板）
    for i in range(24, 0, -1):
        level_name = f'{i}进{i+1}'
        if level_name in data['lianban_progress']:
            ratio = data['lianban_progress'][level_name]
            # 从"1/3=33%"中提取总数3
            if '/' in ratio:
                total_count = int(ratio.split('/')[1].split('=')[0])
                level_counts[level_name] = total_count
                level_order.append(level_name)

    # 添加首板
    if '首板' in data['lianban_progress']:
        ratio = data['lianban_progress']['首板']
        if '/' in ratio:
            total_count = int(ratio.split('/')[1].split('=')[0])
            level_counts['首板'] = total_count
            level_order.append('首板')

    print(f"连板级别分配: {level_counts}")

    # 按顺序分配股票到不同级别
    stock_index = 0
    for level_name in level_order:
        count = level_counts[level_name]
        for i in range(count):
            if stock_index < len(all_stocks):
                stock = all_stocks[stock_index]
                status_emoji = {"成": "✅", "败": "❌", "炸": "💥"}.get(stock['status'], "")

                stock_info = {
                    'name': stock['name'],
                    'code': stock['code'],
                    'status': stock['status'],
                    'status_emoji': status_emoji,
                    'change_percent': stock['change_percent'],
                    'industry': stock['industry'],
                    'level': level_name
                }

                data['stocks_by_level'][level_name].append(stock_info)
                stock_index += 1

    # 如果还有剩余股票，分配到首板
    while stock_index < len(all_stocks):
        stock = all_stocks[stock_index]
        status_emoji = {"成": "✅", "败": "❌", "炸": "💥"}.get(stock['status'], "")

        stock_info = {
            'name': stock['name'],
            'code': stock['code'],
            'status': stock['status'],
            'status_emoji': status_emoji,
            'change_percent': stock['change_percent'],
            'industry': stock['industry'],
            'level': '首板'
        }

        data['stocks_by_level']['首板'].append(stock_info)
        stock_index += 1

    # 对每个级别的股票进行排序
    for level in data['stocks_by_level']:
        data['stocks_by_level'][level] = sort_stocks_by_priority(data['stocks_by_level'][level])
    
    # 提取概念统计（简化版）
    concept_keywords = [
        '机器人概念', '新疆板块', '数据中心', '商业航天', '固态电池', 
        '创新药', '锂矿', '军工', '国防军工', '人脑工程', '水泥', '基础建设'
    ]
    
    for keyword in concept_keywords:
        pattern = f'{keyword}[^\\d]*(\\d+/\\d+)'
        match = re.search(pattern, html_content)
        if match:
            ratio = match.group(1)
            success, total = ratio.split('/')
            success_rate = f"{int(success) / int(total) * 100:.1f}%" if int(total) > 0 else "0%"
            
            data['concept_stats'].append({
                'concept': keyword,
                'ratio': ratio,
                'success_rate': success_rate
            })
    
    return data

def sort_stocks_by_priority(stocks):
    """
    按优先级排序股票：
    1. 成功的股票在前
    2. 炸的股票在中间（按涨幅降序）
    3. 败的股票在后（按涨幅降序）
    """
    def get_sort_key(stock):
        status = stock.get('status', '')
        change_percent = stock.get('change_percent', '0')

        # 将涨跌幅转换为数字，处理可能的异常情况
        try:
            percent_value = float(change_percent) if change_percent else 0
        except (ValueError, TypeError):
            percent_value = 0

        # 返回排序键：(优先级, -涨跌幅)
        # 优先级：成功=1, 炸=2, 败=3, 其他=4
        # 负号是为了降序排列涨跌幅
        if status == '成':
            return (1, -percent_value)
        elif status == '炸':
            return (2, -percent_value)
        elif status == '败':
            return (3, -percent_value)
        else:
            return (4, -percent_value)

    return sorted(stocks, key=get_sort_key)

def print_classified_data(data):
    """打印分类数据"""
    print("\n" + "="*100)
    print("🚀 大板客连板雁阵图数据 - 按级别完整分类")
    print("="*100)
    
    if data.get('update_time'):
        print(f"📅 数据更新时间: {data['update_time']}")
    print()
    
    # 连板进度统计
    if data.get('lianban_progress'):
        print("📊 连板进度统计:")
        print("-"*70)
        for level, ratio in data['lianban_progress'].items():
            success_rate = ratio.split('=')[1] if '=' in ratio else ratio
            stock_count = len(data.get('stocks_by_level', {}).get(level, []))
            print(f"  {level:>6}: {ratio:<15} (成功率: {success_rate:<6}) - 实际提取: {stock_count}只")
        print()
    
    # 按级别显示股票 - 动态生成级别顺序
    level_order = []
    level_emojis = {}

    # 从高级别到低级别排序
    for i in range(24, 0, -1):
        level_name = f'{i}进{i+1}'
        if level_name in data.get('stocks_by_level', {}) and data['stocks_by_level'][level_name]:
            level_order.append(level_name)
            # 根据级别高低设置火焰数量
            if i >= 20:
                level_emojis[level_name] = '🔥' * 10
            elif i >= 15:
                level_emojis[level_name] = '🔥' * 8
            elif i >= 10:
                level_emojis[level_name] = '🔥' * 6
            elif i >= 5:
                level_emojis[level_name] = '🔥' * (i - 1)
            else:
                level_emojis[level_name] = '🔥' * i

    # 添加首板
    if '首板' in data.get('stocks_by_level', {}) and data['stocks_by_level']['首板']:
        level_order.append('首板')
        level_emojis['首板'] = '⭐'
    
    total_stocks = 0
    total_success = 0
    total_fail = 0
    total_bomb = 0
    
    for level in level_order:
        stocks = data.get('stocks_by_level', {}).get(level, [])
        if stocks:
            print(f"{level_emojis.get(level, '🔥')} {level} 级别股票 ({len(stocks)}只):")
            print("-"*90)
            print(f"{'序号':<4} {'股票名称':<12} {'代码':<8} {'状态':<8} {'涨幅':<8} {'行业/概念':<20}")
            print("-"*90)
            
            level_success = level_fail = level_bomb = 0
            
            for i, stock in enumerate(stocks, 1):
                status_display = f"{stock['status_emoji']}{stock['status']}"
                percent_display = f"{stock['change_percent']}%" if stock['change_percent'] else ""
                industry = stock.get('industry', '')[:18]
                
                print(f"{i:<4} {stock['name']:<12} {stock['code']:<8} {status_display:<8} {percent_display:<8} {industry:<20}")
                
                # 统计
                if stock['status'] == '成':
                    level_success += 1
                elif stock['status'] == '败':
                    level_fail += 1
                elif stock['status'] == '炸':
                    level_bomb += 1
            
            level_total = len(stocks)
            level_success_rate = (level_success / level_total * 100) if level_total > 0 else 0
            print(f"\n  📊 {level} 统计: 总计{level_total}只, 成功{level_success}只✅, 失败{level_fail}只❌, 炸板{level_bomb}只💥, 成功率{level_success_rate:.1f}%")
            print()
            
            total_stocks += level_total
            total_success += level_success
            total_fail += level_fail
            total_bomb += level_bomb
    
    # 概念统计
    if data.get('concept_stats'):
        print("🎯 热门概念板块统计:")
        print("-"*60)
        for i, concept in enumerate(data['concept_stats'], 1):
            print(f"{i:2d}. {concept['concept']:<20} {concept['ratio']:<8} (成功率: {concept['success_rate']})")
        print()
    
    # 总体统计
    if total_stocks > 0:
        overall_success_rate = (total_success / total_stocks * 100)
        print("📊 整体市场情绪分析:")
        print("-"*70)
        print(f"  总股票数: {total_stocks}只")
        print(f"  成功(成): {total_success}只 ✅  失败(败): {total_fail}只 ❌  炸板(炸): {total_bomb}只 💥")
        print(f"  整体成功率: {overall_success_rate:.1f}%")
        
        if overall_success_rate >= 60:
            print("  🎉 市场情绪: 强势 - 连板氛围良好，适合追涨")
        elif overall_success_rate >= 40:
            print("  😐 市场情绪: 中性 - 谨慎观望，精选个股")
        else:
            print("  😰 市场情绪: 弱势 - 避险为主，控制仓位")
    
    print("="*100)

def get_dabanke_data_for_date(date_str, save_to_file=True, print_data=True):
    """获取指定日期的大板客数据

    Args:
        date_str: 日期字符串，格式为YYYYMMDD
        save_to_file: 是否保存到文件
        print_data: 是否打印数据

    Returns:
        dict: 提取的数据，如果失败返回None
    """
    print(f"🔄 正在获取 {date_str} 的连板雁阵图数据...")

    html_content = get_dabanke_data(date_str)
    if not html_content:
        return None

    print(f"✅ {date_str} 数据获取成功，正在解析...")

    # 提取并分类数据
    data = extract_all_stocks_and_classify(html_content)

    if not data or not data.get('stocks_by_level'):
        print(f"⚠️ {date_str} 没有有效的股票数据")
        return None

    print(f"✅ {date_str} 连板雁阵图数据分类完成")

    # 打印分类数据
    if print_data:
        print_classified_data(data)

    # 保存数据
    if save_to_file:
        date_str= datetime.strptime(date_str, "%Y%m%d").strftime("%Y-%m-%d")
        filename = f"dabanke_data/dabanke_data_{date_str}.json"
        try:
            import os
            os.makedirs("dabanke_data", exist_ok=True)

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"💾 {date_str} 数据已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")

    return data

def main():
    """主函数"""
    # 如果直接运行此脚本，获取当前数据
    print("🔄 正在获取大板客网站连板雁阵图数据...")

    html_content = get_dabanke_data()
    if not html_content:
        return

    print("✅ 数据获取成功，正在解析并分类连板雁阵图...")

    # 提取并分类数据
    data = extract_all_stocks_and_classify(html_content)

    print("✅ 连板雁阵图数据分类完成")

    # 打印分类数据
    print_classified_data(data)

    # 保存数据
    timestamp = datetime.now().strftime("%Y-%m-%d")
    filename = f"dabanke_data/dabanke_data_{timestamp}.json"

    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"\n💾 完整分类数据已保存到: {filename}")
        print("📝 包含按连板级别分类的股票、进度统计和概念数据")
        print("🎯 可用于连板策略分析和市场情绪判断")
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")

if __name__ == "__main__":
    main()
