#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大板客网站连板雁阵图数据提取脚本 - 按级别分类版本
根据实际HTML结构提取并分类显示股票信息
"""

import requests
import re
from datetime import datetime
import json

def get_dabanke_data():
    """获取大板客网站数据"""
    url = "https://dabanke.com/"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        response.encoding = 'utf-8'
        return response.text
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return None

def extract_lianban_data_classified(html_content):
    """提取并分类连板雁阵图数据"""
    data = {
        'update_time': '',
        'lianban_progress': {},
        'stocks_by_level': {
            '5进6': [],
            '4进5': [],
            '2进3': [],
            '1进2': [],
            '首板': []
        }
    }
    
    # 提取更新时间
    time_match = re.search(r'最后更新时间:\s*(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', html_content)
    if time_match:
        data['update_time'] = time_match.group(1)
    
    # 提取连板进度统计
    progress_patterns = [
        (r'5进6.*?(\d+/\d+=\d+%)', '5进6'),
        (r'4进5.*?(\d+/\d+=\d+%)', '4进5'),
        (r'2进3.*?(\d+/\d+=\d+%)', '2进3'),
        (r'1进2.*?(\d+/\d+=\d+%)', '1进2'),
        (r'首板.*?(\d+/\d+=\d+%)', '首板')
    ]
    
    for pattern, level in progress_patterns:
        match = re.search(pattern, html_content, re.DOTALL)
        if match:
            data['lianban_progress'][level] = match.group(1)
    
    # 按行分析HTML，根据位置推断股票级别
    lines = html_content.split('\n')
    current_level = None
    level_line_ranges = {}
    
    # 首先找到每个级别的起始行
    for i, line in enumerate(lines):
        for level in ['5进6', '4进5', '2进3', '1进2', '首板']:
            if level in line and len(line.strip()) < 100:  # 级别标题行通常较短
                level_line_ranges[level] = i
                break
    
    # 为每个级别定义搜索范围
    search_ranges = {}
    levels = ['5进6', '4进5', '2进3', '1进2', '首板']
    
    for i, level in enumerate(levels):
        if level in level_line_ranges:
            start_line = level_line_ranges[level]
            # 结束行是下一个级别的开始行，或者向后搜索100行
            if i + 1 < len(levels) and levels[i + 1] in level_line_ranges:
                end_line = level_line_ranges[levels[i + 1]]
            else:
                end_line = start_line + 100
            search_ranges[level] = (start_line, end_line)
    
    # 在每个级别的范围内提取股票
    for level, (start_line, end_line) in search_ranges.items():
        stocks = extract_stocks_in_range(lines, start_line, end_line, level)
        data['stocks_by_level'][level] = stocks
    
    return data

def extract_stocks_in_range(lines, start_line, end_line, level):
    """在指定行范围内提取股票信息"""
    stocks = []
    seen_codes = set()
    
    for i in range(start_line, min(end_line, len(lines))):
        line = lines[i]
        
        # 查找股票链接
        stock_matches = re.findall(r'<a[^>]*href="/gupiao-(\d+)\.html"[^>]*>([^<]+)</a>', line)
        
        for code, name in stock_matches:
            name = name.strip()
            if len(name) > 1 and code not in seen_codes:
                seen_codes.add(code)
                
                # 在当前行和后续几行中查找状态和涨跌幅
                context_text = ''
                for j in range(i, min(i + 8, len(lines))):
                    context_text += lines[j] + ' '
                
                # 提取状态
                status_match = re.search(r'\(([成败炸])\)', context_text)
                status = status_match.group(1) if status_match else ''
                
                # 提取涨跌幅
                percent_match = re.search(r'([-+]?\d+\.?\d*)\s*%', context_text)
                percent = percent_match.group(1) if percent_match else ''
                
                # 提取行业信息（在%符号后面的文字）
                industry_match = re.search(r'%\s*\]\s*([^<\n(]+?)(?:\s*$|\s*\(|\s*<|\s*\n)', context_text)
                industry = industry_match.group(1).strip() if industry_match else ''
                
                # 只保存有状态或涨跌幅的股票
                if status or percent:
                    status_emoji = {"成": "✅", "败": "❌", "炸": "💥"}.get(status, "")
                    stocks.append({
                        'name': name,
                        'code': code,
                        'status': status,
                        'status_emoji': status_emoji,
                        'change_percent': percent,
                        'industry': industry,
                        'level': level
                    })
                
                # 每个级别最多提取15只股票
                if len(stocks) >= 15:
                    break
    
    return stocks

def print_classified_data(data):
    """打印分类后的数据"""
    print("\n" + "="*95)
    print("🚀 大板客连板雁阵图数据 - 按连板级别分类")
    print("="*95)
    
    if data.get('update_time'):
        print(f"📅 数据更新时间: {data['update_time']}")
    print()
    
    # 连板进度统计
    if data.get('lianban_progress'):
        print("📊 连板进度统计:")
        print("-"*70)
        for level, ratio in data['lianban_progress'].items():
            success_rate = ratio.split('=')[1] if '=' in ratio else ratio
            stock_count = len(data.get('stocks_by_level', {}).get(level, []))
            print(f"  {level:>6}: {ratio:<15} (成功率: {success_rate:<6}) - 实际提取: {stock_count}只")
        print()
    
    # 按级别显示股票
    level_order = ['5进6', '4进5', '2进3', '1进2', '首板']
    level_emojis = {
        '5进6': '🔥🔥🔥🔥🔥',
        '4进5': '🔥🔥🔥🔥',
        '2进3': '🔥🔥🔥',
        '1进2': '🔥🔥',
        '首板': '🔥'
    }
    
    total_stocks = 0
    total_success = 0
    total_fail = 0
    total_bomb = 0
    
    for level in level_order:
        stocks = data.get('stocks_by_level', {}).get(level, [])
        if stocks:
            print(f"{level_emojis.get(level, '🔥')} {level} 级别股票 ({len(stocks)}只):")
            print("-"*90)
            print(f"{'序号':<4} {'股票名称':<12} {'代码':<8} {'状态':<8} {'涨幅':<8} {'行业/概念':<25}")
            print("-"*90)
            
            level_success = 0
            level_fail = 0
            level_bomb = 0
            
            for i, stock in enumerate(stocks, 1):
                status_display = f"{stock['status_emoji']}{stock['status']}"
                percent_display = f"{stock['change_percent']}%" if stock['change_percent'] else ""
                industry = stock.get('industry', '')[:23] if stock.get('industry') else ''
                
                print(f"{i:<4} {stock['name']:<12} {stock['code']:<8} {status_display:<8} {percent_display:<8} {industry:<25}")
                
                # 统计
                if stock['status'] == '成':
                    level_success += 1
                elif stock['status'] == '败':
                    level_fail += 1
                elif stock['status'] == '炸':
                    level_bomb += 1
            
            # 显示该级别统计
            level_total = len(stocks)
            level_success_rate = (level_success / level_total * 100) if level_total > 0 else 0
            print(f"\n  📊 {level} 统计: 总计{level_total}只, 成功{level_success}只, 失败{level_fail}只, 炸板{level_bomb}只, 成功率{level_success_rate:.1f}%")
            print()
            
            total_stocks += level_total
            total_success += level_success
            total_fail += level_fail
            total_bomb += level_bomb
    
    # 总体统计
    if total_stocks > 0:
        overall_success_rate = (total_success / total_stocks * 100)
        print("📊 整体统计摘要:")
        print("-"*70)
        print(f"  总股票数: {total_stocks}只")
        print(f"  成功(成): {total_success}只 ✅  失败(败): {total_fail}只 ❌  炸板(炸): {total_bomb}只 💥")
        print(f"  整体成功率: {overall_success_rate:.1f}%")
        
        if overall_success_rate >= 60:
            print("  🎉 市场情绪: 强势")
        elif overall_success_rate >= 40:
            print("  😐 市场情绪: 中性")
        else:
            print("  😰 市场情绪: 弱势")
    
    print("="*95)

def main():
    """主函数"""
    print("🔄 正在获取大板客网站连板雁阵图数据...")
    
    html_content = get_dabanke_data()
    if not html_content:
        return
    
    print("✅ 网站数据获取成功，正在按级别分类解析...")
    
    # 提取分类数据
    data = extract_lianban_data_classified(html_content)
    
    print("✅ 连板雁阵图数据分类解析完成")
    
    # 打印分类数据
    print_classified_data(data)
    
    # 保存数据
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"dabanke_classified_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"\n💾 分类数据已保存到: {filename}")
        print("📝 数据包含按连板级别分类的详细股票信息")
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")

if __name__ == "__main__":
    main()
