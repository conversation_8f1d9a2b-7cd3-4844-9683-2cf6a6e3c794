// 股票数据可视化图表脚本

// 全局变量
let lianbanChart, marketChart, limitChart, volumeChart;

// 颜色配置
const colors = {
    primary: '#007bff',
    success: '#28a745',
    warning: '#ffc107',
    danger: '#dc3545',
    info: '#17a2b8',
    light: '#f8f9fa',
    dark: '#343a40'
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    initializeCharts();
    loadAllData();

    // 设置定时刷新（每5分钟）
    setInterval(loadAllData, 5 * 60 * 1000);
});

// 初始化所有图表
function initializeCharts() {
    // 连板进度图表
    const lianbanCtx = document.getElementById('lianbanChart').getContext('2d');
    lianbanChart = new Chart(lianbanCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '连板成功率趋势'
                },
                legend: {
                    position: 'bottom'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function (value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });

    // 市场涨跌分布图表
    const marketCtx = document.getElementById('marketChart').getContext('2d');
    marketChart = new Chart(marketCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '市场涨跌家数趋势'
                },
                legend: {
                    position: 'bottom'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // 涨跌停统计图表
    const limitCtx = document.getElementById('limitChart').getContext('2d');
    limitChart = new Chart(limitCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '涨跌停数量统计'
                },
                legend: {
                    position: 'bottom'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // 成交金额图表
    const volumeCtx = document.getElementById('volumeChart').getContext('2d');
    volumeChart = new Chart(volumeCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '成交金额趋势'
                },
                legend: {
                    position: 'bottom'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function (value) {
                            return formatAmount(value);
                        }
                    }
                }
            }
        }
    });
}

// 加载所有数据
async function loadAllData() {
    try {
        await Promise.all([
            loadLianbanData(),
            loadMarketData(),
            loadLimitData()
        ]);

        updateLastUpdateTime();
    } catch (error) {
        console.error('加载数据失败:', error);
        showError('数据加载失败，请稍后重试');
    }
}

// 加载连板进度数据
async function loadLianbanData() {
    try {
        const response = await fetch('/api/lianban_progress');
        const result = await response.json();

        if (result.success) {
            updateLianbanChart(result.data);
            updateLianbanSummary(result.data);
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('连板数据加载失败:', error);
    }
}

// 加载市场数据
async function loadMarketData() {
    try {
        const response = await fetch('/api/market_summary');
        const result = await response.json();

        if (result.success) {
            updateMarketChart(result.data);
            updateVolumeChart(result.data);
            updateMarketSummary(result.data);
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('市场数据加载失败:', error);
    }
}

// 加载涨跌停数据
async function loadLimitData() {
    try {
        const response = await fetch('/api/limit_stats');
        const result = await response.json();

        if (result.success) {
            updateLimitChart(result.data);
            updateLimitSummary(result.data);
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('涨跌停数据加载失败:', error);
    }
}

// 更新连板进度图表
function updateLianbanChart(data) {
    if (!data || data.length === 0) return;

    const dates = data.map(item => formatDate(item.date));
    const levels = ['首板', '1进2', '2进3', '3进4', '5进6', '9进10'];

    const datasets = levels.map((level, index) => ({
        label: level,
        data: data.map(item => item.progress[level]?.percentage || 0),
        borderColor: getColorByIndex(index),
        backgroundColor: getColorByIndex(index, 0.1),
        fill: false,
        tension: 0.1
    }));

    lianbanChart.data.labels = dates;
    lianbanChart.data.datasets = datasets;
    lianbanChart.update();
}

// 更新市场图表
function updateMarketChart(data) {
    if (!data || data.length === 0) return;

    const dates = data.map(item => formatDate(item.date));

    marketChart.data.labels = dates;
    marketChart.data.datasets = [
        {
            label: '上涨家数',
            data: data.map(item => item.up_count),
            borderColor: colors.success,
            backgroundColor: colors.success + '20',
            fill: false
        },
        {
            label: '下跌家数',
            data: data.map(item => item.down_count),
            borderColor: colors.danger,
            backgroundColor: colors.danger + '20',
            fill: false
        }
    ];
    marketChart.update();
}

// 更新涨跌停图表
function updateLimitChart(data) {
    if (!data || data.length === 0) return;

    const dates = data.map(item => formatDate(item.date));

    limitChart.data.labels = dates;
    limitChart.data.datasets = [
        {
            label: '涨停数量',
            data: data.map(item => item.limit_up_total),
            backgroundColor: colors.danger,
            borderColor: colors.danger,
            borderWidth: 1
        },
        {
            label: '跌停数量',
            data: data.map(item => item.limit_down_total),
            backgroundColor: colors.success,
            borderColor: colors.success,
            borderWidth: 1
        }
    ];
    limitChart.update();
}

// 更新成交金额图表
function updateVolumeChart(data) {
    if (!data || data.length === 0) return;

    const dates = data.map(item => formatDate(item.date));

    volumeChart.data.labels = dates;
    volumeChart.data.datasets = [
        {
            label: '成交金额',
            data: data.map(item => item.total_amount),
            borderColor: colors.primary,
            backgroundColor: colors.primary + '20',
            fill: true,
            tension: 0.1
        }
    ];
    volumeChart.update();
}

// 工具函数
function formatDate(dateStr) {
    const date = new Date(dateStr);
    return `${date.getMonth() + 1}/${date.getDate()}`;
}

function formatAmount(amount) {
    if (amount >= 1e12) {
        return (amount / 1e12).toFixed(1) + '万亿';
    } else if (amount >= 1e8) {
        return (amount / 1e8).toFixed(1) + '亿';
    } else if (amount >= 1e4) {
        return (amount / 1e4).toFixed(1) + '万';
    }
    return amount.toString();
}

function getColorByIndex(index, alpha = 1) {
    const colorList = [
        `rgba(255, 99, 132, ${alpha})`,
        `rgba(54, 162, 235, ${alpha})`,
        `rgba(255, 205, 86, ${alpha})`,
        `rgba(75, 192, 192, ${alpha})`,
        `rgba(153, 102, 255, ${alpha})`,
        `rgba(255, 159, 64, ${alpha})`
    ];
    return colorList[index % colorList.length];
}

function updateLastUpdateTime() {
    const now = new Date();
    const timeStr = now.toLocaleString('zh-CN');
    document.getElementById('last-update').textContent = timeStr;
}

function showError(message) {
    console.error(message);
    // 这里可以添加错误提示UI
}

// 更新连板概览
function updateLianbanSummary(data) {
    if (!data || data.length === 0) return;

    const latest = data[data.length - 1];
    const progress = latest.progress;

    let html = '<div class="row">';
    Object.keys(progress).forEach(level => {
        const item = progress[level];
        html += `
            <div class="col-6 col-md-4 mb-2">
                <div class="summary-item">
                    <div class="summary-value">${item.percentage}%</div>
                    <div class="summary-label">${level}</div>
                    <small class="text-muted">${item.success}/${item.total}</small>
                </div>
            </div>
        `;
    });
    html += '</div>';

    document.getElementById('lianban-summary').innerHTML = html;
}

// 更新市场概览
function updateMarketSummary(data) {
    if (!data || data.length === 0) return;

    const latest = data[data.length - 1];

    const html = `
        <div class="row">
            <div class="col-4">
                <div class="summary-item">
                    <div class="summary-value status-positive">${latest.up_count}</div>
                    <div class="summary-label">上涨</div>
                </div>
            </div>
            <div class="col-4">
                <div class="summary-item">
                    <div class="summary-value status-negative">${latest.down_count}</div>
                    <div class="summary-label">下跌</div>
                </div>
            </div>
            <div class="col-4">
                <div class="summary-item">
                    <div class="summary-value">${latest.total_amount_formatted}</div>
                    <div class="summary-label">成交额</div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('market-summary').innerHTML = html;
}

// 更新涨跌停概览
function updateLimitSummary(data) {
    if (!data || data.length === 0) return;

    const latest = data[data.length - 1];

    const html = `
        <div class="row">
            <div class="col-6">
                <div class="summary-item">
                    <div class="summary-value status-positive">${latest.limit_up_total}</div>
                    <div class="summary-label">涨停</div>
                    <small class="text-muted">非ST: ${latest.limit_up_non_st}</small>
                </div>
            </div>
            <div class="col-6">
                <div class="summary-item">
                    <div class="summary-value status-negative">${latest.limit_down_total}</div>
                    <div class="summary-label">跌停</div>
                    <small class="text-muted">非ST: ${latest.limit_down_non_st}</small>
                </div>
            </div>
        </div>
    `;

    document.getElementById('limit-summary').innerHTML = html;
}
