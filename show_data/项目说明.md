# 股票数据可视化系统

## 项目概述

这是一个基于Flask的股票数据可视化Web应用，专门用于展示股票市场的连板进度、市场概况和涨跌停统计等关键数据的时间趋势。

## 主要功能

### 📊 数据展示
- **连板进度趋势**: 显示首板、1进2、2进3、3进4、5进6、9进10各级别连板的成功率变化
- **市场涨跌分布**: 展示每日市场上涨和下跌股票数量统计
- **涨跌停统计**: 涨停和跌停股票数量的时间趋势分析
- **成交金额趋势**: 市场总成交金额的变化情况

### 🎨 界面特性
- **响应式设计**: 支持桌面端和移动端访问
- **实时数据**: 每5分钟自动刷新数据
- **美观图表**: 使用Chart.js创建交互式图表
- **直观概览**: 卡片式数据概览，一目了然

### 🔧 技术特性
- **模块化设计**: 清晰的代码结构，易于维护和扩展
- **配置管理**: 支持开发、测试、生产环境配置
- **错误处理**: 完善的异常处理和用户提示
- **日志记录**: 详细的应用日志记录

## 数据源

系统读取两个主要数据源：

1. **打板客数据** (`../dabanke_data/`)
   - 文件格式: `dabanke_data_YYYY-MM-DD.json`
   - 包含连板进度信息
   - 数据结构: 各级别连板的成功率统计

2. **市场数据** (`../market_data/`)
   - 文件格式: `market_data_YYYY-MM-DD.json`
   - 包含市场概况和涨跌停统计
   - 数据结构: 涨跌家数、成交金额、涨跌停数量

## 快速开始

### 1. 环境准备
```bash
cd show_data
pip install -r requirements.txt
```

### 2. 启动应用
```bash
# 方式1: 使用启动脚本
python run.py

# 方式2: 直接运行应用
python app.py

# 方式3: 使用部署脚本
./deploy.sh
```

### 3. 访问应用
打开浏览器访问: http://localhost:5000

## 项目结构

```
show_data/
├── app.py              # Flask应用主文件
├── run.py              # 启动脚本
├── config.py           # 配置文件
├── requirements.txt    # Python依赖
├── deploy.sh           # 部署脚本
├── README.md          # 英文说明文档
├── 项目说明.md         # 中文说明文档
├── templates/         # HTML模板
│   └── index.html     # 主页面模板
└── static/           # 静态资源
    ├── css/
    │   └── style.css  # 样式文件
    └── js/
        ├── charts.js  # 图表脚本
        └── utils.js   # 工具函数
```

## API接口

### RESTful API
- `GET /` - 主页面
- `GET /api/lianban_progress` - 获取连板进度数据
- `GET /api/market_summary` - 获取市场概况数据
- `GET /api/limit_stats` - 获取涨跌停统计数据

### 响应格式
```json
{
  "success": true,
  "data": [...]
}
```

## 配置说明

### 环境变量
- `FLASK_CONFIG`: 配置环境 (development/production/testing)
- `FLASK_HOST`: 服务器地址 (默认: 0.0.0.0)
- `FLASK_PORT`: 服务器端口 (默认: 5000)
- `FLASK_DEBUG`: 调试模式 (默认: True)
- `DABANKE_DATA_PATH`: 打板客数据路径
- `MARKET_DATA_PATH`: 市场数据路径

### 配置文件
详细配置请参考 `config.py` 文件。

## 部署建议

### 开发环境
```bash
python run.py
```

### 生产环境
建议使用WSGI服务器如Gunicorn：
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## 扩展开发

### 添加新的数据展示
1. 在 `app.py` 中添加新的API路由
2. 在 `templates/index.html` 中添加新的图表容器
3. 在 `static/js/charts.js` 中实现图表逻辑
4. 在 `static/css/style.css` 中添加样式

### 修改数据源
1. 更新 `config.py` 中的数据路径配置
2. 修改 `app.py` 中的数据读取函数
3. 调整数据处理逻辑以适应新的数据格式

## 注意事项

1. **数据文件**: 确保数据目录存在且包含有效的JSON文件
2. **文件命名**: 数据文件必须按照指定格式命名
3. **端口占用**: 确保5000端口未被其他应用占用
4. **浏览器兼容**: 建议使用现代浏览器以获得最佳体验
5. **数据更新**: 系统会自动读取最新的数据文件

## 故障排除

### 常见问题
1. **404错误**: 检查路由配置和模板文件
2. **数据加载失败**: 检查数据文件路径和格式
3. **图表不显示**: 检查JavaScript控制台错误
4. **样式异常**: 检查CSS文件加载情况

### 日志查看
应用日志会输出到控制台和日志文件，可以通过日志排查问题。

## 技术栈

- **后端**: Flask 2.3.3 (Python)
- **前端**: Bootstrap 5 + Chart.js
- **图表库**: Chart.js
- **样式**: CSS3 + Bootstrap
- **字体图标**: Font Awesome

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目仓库: [GitHub链接]
- 邮箱: [联系邮箱]
