# 股票数据可视化系统

一个基于Flask的股票数据可视化Web应用，用于展示连板进度、市场概况和涨跌停统计等数据的时间趋势。

## 功能特性

- 📊 **连板进度趋势图**: 显示各级别连板的成功率变化
- 📈 **市场涨跌分布**: 展示每日涨跌家数统计
- 📉 **涨跌停统计**: 涨停和跌停股票数量趋势
- 💰 **成交金额趋势**: 市场成交金额变化情况
- 🎨 **美观界面**: 响应式设计，支持移动端访问
- 🔄 **自动刷新**: 每5分钟自动更新数据

## 数据源

系统读取以下两个目录中的JSON数据文件：

- `../dabanke_data/`: 打板客数据，包含连板进度信息
- `../market_data/`: 市场数据，包含市场概况和涨跌停统计

## 安装和运行

### 1. 安装依赖

```bash
cd show_data
pip install -r requirements.txt
```

### 2. 启动应用

```bash
python run.py
```

或者直接运行：

```bash
python app.py
```

### 3. 访问应用

打开浏览器访问: http://localhost:5000

## 项目结构

```
show_data/
├── app.py              # Flask应用主文件
├── run.py              # 启动脚本
├── requirements.txt    # Python依赖
├── README.md          # 说明文档
├── templates/         # HTML模板
│   └── index.html     # 主页面模板
└── static/           # 静态资源
    ├── css/
    │   └── style.css  # 样式文件
    └── js/
        └── charts.js  # 图表脚本
```

## API接口

系统提供以下RESTful API接口：

- `GET /api/lianban_progress` - 获取连板进度数据
- `GET /api/market_summary` - 获取市场概况数据  
- `GET /api/limit_stats` - 获取涨跌停统计数据

## 数据格式

### 连板进度数据格式 (dabanke_data)
```json
{
  "update_time": "2025-05-20 15:09:11",
  "lianban_progress": {
    "9进10": "0/1=0%",
    "5进6": "0/3=0%",
    "3进4": "2/2=100%",
    "2进3": "6/17=35%",
    "1进2": "18/59=31%",
    "首板": "51/76=67%"
  }
}
```

### 市场数据格式 (market_data)
```json
{
  "date": "2025-05-16",
  "market_summary": {
    "up_count": 3002,
    "down_count": 3167,
    "total_amount": *************.6399,
    "total_amount_formatted": "1.09万亿"
  },
  "limit_stats": {
    "limit_up": {
      "total": 67,
      "non_st": 56,
      "st": 11
    },
    "limit_down": {
      "total": 11,
      "non_st": 3,
      "st": 8
    }
  }
}
```

## 技术栈

- **后端**: Flask (Python)
- **前端**: Bootstrap 5 + Chart.js
- **图表**: Chart.js
- **样式**: CSS3 + Bootstrap

## 注意事项

1. 确保数据目录存在且包含有效的JSON文件
2. 数据文件命名格式: `dabanke_data_YYYY-MM-DD.json` 和 `market_data_YYYY-MM-DD.json`
3. 系统会自动按日期排序读取数据文件
4. 建议在生产环境中使用WSGI服务器（如Gunicorn）部署

## 开发和扩展

如需添加新的数据展示或修改现有功能：

1. 修改 `app.py` 中的数据处理逻辑
2. 更新 `templates/index.html` 中的页面结构
3. 调整 `static/js/charts.js` 中的图表配置
4. 修改 `static/css/style.css` 中的样式

## 许可证

MIT License
