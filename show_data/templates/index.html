<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票数据可视化</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
</head>

<body>
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-center text-primary mb-3">
                    <i class="fas fa-chart-line"></i> 股票市场数据分析
                </h1>
                <p class="text-center text-muted">实时监控连板进度、市场概况和涨跌停统计</p>
            </div>
        </div>

        <!-- 数据概览卡片 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-layer-group"></i> 连板进度</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">显示各级别连板的成功率趋势</p>
                        <div id="lianban-summary" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-bar"></i> 市场概况</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">涨跌家数和成交金额统计</p>
                        <div id="market-summary" class="text-center">
                            <div class="spinner-border text-success" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> 涨跌停统计</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">涨停和跌停股票数量趋势</p>
                        <div id="limit-summary" class="text-center">
                            <div class="spinner-border text-warning" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row">
            <!-- 连板进度图表 -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">连板进度趋势</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="lianbanChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- 市场概况图表 -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">市场涨跌分布</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="marketChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- 涨跌停统计图表 -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">涨跌停数量趋势</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="limitChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- 成交金额图表 -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">成交金额趋势</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="volumeChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <footer class="row mt-5">
            <div class="col-12 text-center text-muted">
                <p>&copy; 2025 股票数据可视化系统 | 数据更新时间: <span id="last-update">加载中...</span></p>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="{{ url_for('static', filename='js/utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/charts.js') }}"></script>
</body>

</html>