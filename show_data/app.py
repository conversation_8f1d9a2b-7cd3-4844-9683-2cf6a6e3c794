#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据可视化Web应用
显示连板进度、市场概况、涨跌停统计等数据的时间趋势
"""

from flask import Flask, render_template, jsonify
import json
import os
import glob
from datetime import datetime
from collections import defaultdict
import re
import logging
from config import config

# 创建Flask应用
def create_app(config_name=None):
    app = Flask(__name__)

    # 加载配置
    config_name = config_name or os.environ.get('FLASK_CONFIG') or 'default'
    app.config.from_object(config[config_name])

    # 配置日志
    if not app.debug:
        logging.basicConfig(
            level=getattr(logging, app.config['LOG_LEVEL']),
            format='%(asctime)s %(levelname)s: %(message)s',
            handlers=[
                logging.FileHandler(app.config['LOG_FILE']),
                logging.StreamHandler()
            ]
        )

    return app

app = create_app()

# 数据文件路径
DABANKE_DATA_PATH = app.config['DABANKE_DATA_PATH']
MARKET_DATA_PATH = app.config['MARKET_DATA_PATH']

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/lianban_progress')
def api_lianban_progress():
    """连板进度数据API"""
    try:
        data = load_lianban_progress_data()
        return jsonify({
            'success': True,
            'data': data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/market_summary')
def api_market_summary():
    """市场概况数据API"""
    try:
        data = load_market_summary_data()
        return jsonify({
            'success': True,
            'data': data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/limit_stats')
def api_limit_stats():
    """涨跌停统计数据API"""
    try:
        data = load_limit_stats_data()
        return jsonify({
            'success': True,
            'data': data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def load_lianban_progress_data():
    """加载连板进度数据"""
    data = []
    pattern = os.path.join(DABANKE_DATA_PATH, 'dabanke_data_*.json')
    files = sorted(glob.glob(pattern))
    
    for file_path in files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = json.load(f)
                
            # 从文件名提取日期
            filename = os.path.basename(file_path)
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', filename)
            if not date_match:
                continue
                
            date = date_match.group(1)
            
            if 'lianban_progress' in content:
                progress_data = content['lianban_progress']
                
                # 解析连板进度数据
                parsed_data = {}
                for level, progress_str in progress_data.items():
                    # 解析 "18/59=31%" 格式
                    match = re.search(r'(\d+)/(\d+)=(\d+)%', progress_str)
                    if match:
                        success = int(match.group(1))
                        total = int(match.group(2))
                        percentage = int(match.group(3))
                        parsed_data[level] = {
                            'success': success,
                            'total': total,
                            'percentage': percentage
                        }
                
                data.append({
                    'date': date,
                    'progress': parsed_data
                })
                
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            continue
    
    return data

def load_market_summary_data():
    """加载市场概况数据"""
    data = []
    pattern = os.path.join(MARKET_DATA_PATH, 'market_data_*.json')
    files = sorted(glob.glob(pattern))
    
    for file_path in files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = json.load(f)
                
            if 'date' in content and 'market_summary' in content:
                date = content['date']
                summary = content['market_summary']
                
                data.append({
                    'date': date,
                    'up_count': summary.get('up_count', 0),
                    'down_count': summary.get('down_count', 0),
                    'total_amount': summary.get('total_amount', 0),
                    'total_amount_formatted': summary.get('total_amount_formatted', '0')
                })
                
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            continue
    
    return data

def load_limit_stats_data():
    """加载涨跌停统计数据"""
    data = []
    pattern = os.path.join(MARKET_DATA_PATH, 'market_data_*.json')
    files = sorted(glob.glob(pattern))
    
    for file_path in files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = json.load(f)
                
            if 'date' in content and 'limit_stats' in content:
                date = content['date']
                stats = content['limit_stats']
                
                limit_up = stats.get('limit_up', {})
                limit_down = stats.get('limit_down', {})
                
                data.append({
                    'date': date,
                    'limit_up_total': limit_up.get('total', 0),
                    'limit_up_non_st': limit_up.get('non_st', 0),
                    'limit_up_st': limit_up.get('st', 0),
                    'limit_down_total': limit_down.get('total', 0),
                    'limit_down_non_st': limit_down.get('non_st', 0),
                    'limit_down_st': limit_down.get('st', 0)
                })
                
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            continue
    
    return data

if __name__ == '__main__':
    app.run(
        debug=app.config['DEBUG'],
        host=app.config['HOST'],
        port=app.config['PORT']
    )
