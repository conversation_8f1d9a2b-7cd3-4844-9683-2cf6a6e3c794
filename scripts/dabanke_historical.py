#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大板客历史数据批量获取脚本
获取最近60个工作日的连板雁阵图数据
"""

import os
import sys
import time
from datetime import datetime, timedelta
import json
import requests

# 导入我们的数据提取函数
from dabanke_success import get_dabanke_data_for_date

def is_workday(date):
    """判断是否为工作日（周一到周五）"""
    return date.weekday() < 5

def is_chinese_holiday(date):
    """简单的中国节假日判断（可以根据需要扩展）"""
    # 这里只包含一些固定的节假日，实际使用时可以接入更完整的节假日API
    holidays_2025 = [
        # 元旦
        (1, 1), (1, 2), (1, 3),
        # 春节（示例，实际日期需要查询）
        (1, 28), (1, 29), (1, 30), (1, 31), (2, 1), (2, 2), (2, 3),
        # 清明节
        (4, 5), (4, 6), (4, 7),
        # 劳动节
        (5, 1), (5, 2), (5, 3),
        # 端午节
        (5, 31), (6, 1), (6, 2),
        # 中秋节
        (10, 6), (10, 7), (10, 8),
        # 国庆节
        (10, 1), (10, 2), (10, 3), (10, 4), (10, 5)
    ]
    
    holidays_2024 = [
        # 元旦
        (1, 1),
        # 春节
        (2, 10), (2, 11), (2, 12), (2, 13), (2, 14), (2, 15), (2, 16), (2, 17),
        # 清明节
        (4, 4), (4, 5), (4, 6),
        # 劳动节
        (5, 1), (5, 2), (5, 3), (5, 4), (5, 5),
        # 端午节
        (6, 10),
        # 中秋节
        (9, 15), (9, 16), (9, 17),
        # 国庆节
        (10, 1), (10, 2), (10, 3), (10, 4), (10, 5), (10, 6), (10, 7)
    ]
    
    month_day = (date.month, date.day)
    
    if date.year == 2024:
        return month_day in holidays_2024
    elif date.year == 2025:
        return month_day in holidays_2025
    
    return False

def get_trading_days(start_date, days_count=60):
    """获取指定数量的交易日"""
    trading_days = []
    current_date = start_date
    
    while len(trading_days) < days_count:
        if is_workday(current_date) and not is_chinese_holiday(current_date):
            trading_days.append(current_date)
        current_date -= timedelta(days=1)
    
    return sorted(trading_days)  # 按时间正序排列

def check_existing_data(date_str):
    """检查指定日期的数据是否已存在"""
    filename = f"dabanke_data/dabanke_{date_str}.json"
    return os.path.exists(filename)

def load_existing_data(date_str):
    """加载已存在的数据"""
    filename = f"dabanke_data/dabanke_{date_str}.json"
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载 {date_str} 数据失败: {e}")
        return None

def get_historical_data(days=60, skip_existing=True, delay_seconds=2):
    """获取历史数据
    
    Args:
        days: 要获取的交易日天数
        skip_existing: 是否跳过已存在的数据
        delay_seconds: 请求间隔秒数，避免请求过于频繁
    """
    print(f"🚀 开始获取最近 {days} 个交易日的大板客数据")
    print("="*80)
    
    # 创建数据目录
    os.makedirs("dabanke_data", exist_ok=True)
    
    # 获取交易日列表
    today = datetime.now().date()
    trading_days = get_trading_days(today, days)
    
    print(f"📅 交易日范围: {trading_days[0]} 到 {trading_days[-1]}")
    print(f"📊 总共需要获取 {len(trading_days)} 个交易日的数据")
    print()
    
    success_count = 0
    skip_count = 0
    fail_count = 0
    
    for i, date in enumerate(trading_days, 1):
        date_str = date.strftime("%Y%m%d")
        
        print(f"[{i}/{len(trading_days)}] 处理日期: {date_str} ({date.strftime('%Y-%m-%d %A')})")
        
        # 检查是否已存在
        if skip_existing and check_existing_data(date_str):
            print(f"  ✅ 数据已存在，跳过")
            skip_count += 1
        else:
            # 获取数据
            try:
                data = get_dabanke_data_for_date(date_str, save_to_file=True, print_data=False)
                if data:
                    # 统计股票数量
                    total_stocks = sum(len(stocks) for stocks in data.get('stocks_by_level', {}).values())
                    success_stocks = sum(len([s for s in stocks if s.get('status') == '成']) 
                                       for stocks in data.get('stocks_by_level', {}).values())
                    success_rate = (success_stocks / total_stocks * 100) if total_stocks > 0 else 0
                    
                    print(f"  ✅ 成功获取数据: {total_stocks}只股票, 成功率{success_rate:.1f}%")
                    success_count += 1
                else:
                    print(f"  ❌ 获取数据失败或无数据")
                    fail_count += 1
                
                # 延迟，避免请求过于频繁
                if i < len(trading_days):  # 最后一个不需要延迟
                    time.sleep(delay_seconds)
                    
            except Exception as e:
                print(f"  ❌ 处理失败: {e}")
                fail_count += 1
        
        print()
    
    # 输出统计结果
    print("="*80)
    print("📊 数据获取完成统计:")
    print(f"  ✅ 成功获取: {success_count} 天")
    print(f"  ⏭️  跳过已存在: {skip_count} 天")
    print(f"  ❌ 获取失败: {fail_count} 天")
    print(f"  📁 数据保存目录: dabanke_data/")
    print("="*80)

def analyze_historical_data():
    """分析历史数据，生成统计报告"""
    print("📈 开始分析历史数据...")
    
    data_dir = "dabanke_data"
    if not os.path.exists(data_dir):
        print("❌ 数据目录不存在")
        return
    
    # 获取所有数据文件
    data_files = [f for f in os.listdir(data_dir) if f.startswith("dabanke_") and f.endswith(".json")]
    data_files.sort()
    
    if not data_files:
        print("❌ 没有找到历史数据文件")
        return
    
    print(f"📊 找到 {len(data_files)} 个数据文件")
    
    # 分析数据
    daily_stats = []
    
    for filename in data_files:
        date_str = filename.replace("dabanke_", "").replace(".json", "")
        filepath = os.path.join(data_dir, filename)
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 统计当日数据
            total_stocks = sum(len(stocks) for stocks in data.get('stocks_by_level', {}).values())
            success_stocks = sum(len([s for s in stocks if s.get('status') == '成']) 
                               for stocks in data.get('stocks_by_level', {}).values())
            fail_stocks = sum(len([s for s in stocks if s.get('status') == '败']) 
                            for stocks in data.get('stocks_by_level', {}).values())
            bomb_stocks = sum(len([s for s in stocks if s.get('status') == '炸']) 
                            for stocks in data.get('stocks_by_level', {}).values())
            
            success_rate = (success_stocks / total_stocks * 100) if total_stocks > 0 else 0
            
            daily_stats.append({
                'date': date_str,
                'total_stocks': total_stocks,
                'success_stocks': success_stocks,
                'fail_stocks': fail_stocks,
                'bomb_stocks': bomb_stocks,
                'success_rate': success_rate,
                'update_time': data.get('update_time', '')
            })
            
        except Exception as e:
            print(f"❌ 分析 {filename} 失败: {e}")
    
    # 生成统计报告
    if daily_stats:
        print("\n📊 历史数据统计报告:")
        print("-"*100)
        print(f"{'日期':<12} {'总股票':<8} {'成功':<6} {'失败':<6} {'炸板':<6} {'成功率':<8} {'更新时间':<20}")
        print("-"*100)
        
        for stat in daily_stats[-20:]:  # 显示最近20天
            date_formatted = f"{stat['date'][:4]}-{stat['date'][4:6]}-{stat['date'][6:]}"
            print(f"{date_formatted:<12} {stat['total_stocks']:<8} {stat['success_stocks']:<6} "
                  f"{stat['fail_stocks']:<6} {stat['bomb_stocks']:<6} {stat['success_rate']:<7.1f}% "
                  f"{stat['update_time']:<20}")
        
        # 计算平均值
        avg_success_rate = sum(s['success_rate'] for s in daily_stats) / len(daily_stats)
        avg_total_stocks = sum(s['total_stocks'] for s in daily_stats) / len(daily_stats)
        
        print("-"*100)
        print(f"📈 统计周期: {len(daily_stats)} 个交易日")
        print(f"📊 平均成功率: {avg_success_rate:.1f}%")
        print(f"📊 平均股票数: {avg_total_stocks:.0f}只")
        print("="*100)

def export_to_csv():
    """导出历史数据到CSV文件"""
    import csv

    print("📊 导出历史数据到CSV文件...")

    data_dir = "dabanke_data"
    if not os.path.exists(data_dir):
        print("❌ 数据目录不存在")
        return

    # 获取所有数据文件
    data_files = [f for f in os.listdir(data_dir) if f.startswith("dabanke_") and f.endswith(".json")]
    data_files.sort()

    if not data_files:
        print("❌ 没有找到历史数据文件")
        return

    # 导出每日统计数据
    csv_filename = "dabanke_daily_stats.csv"
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['日期', '总股票数', '成功数', '失败数', '炸板数', '成功率', '更新时间']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for filename in data_files:
            date_str = filename.replace("dabanke_", "").replace(".json", "")
            filepath = os.path.join(data_dir, filename)

            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 统计数据
                total_stocks = sum(len(stocks) for stocks in data.get('stocks_by_level', {}).values())
                success_stocks = sum(len([s for s in stocks if s.get('status') == '成'])
                                   for stocks in data.get('stocks_by_level', {}).values())
                fail_stocks = sum(len([s for s in stocks if s.get('status') == '败'])
                                for stocks in data.get('stocks_by_level', {}).values())
                bomb_stocks = sum(len([s for s in stocks if s.get('status') == '炸'])
                                for stocks in data.get('stocks_by_level', {}).values())

                success_rate = (success_stocks / total_stocks * 100) if total_stocks > 0 else 0

                date_formatted = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:]}"

                writer.writerow({
                    '日期': date_formatted,
                    '总股票数': total_stocks,
                    '成功数': success_stocks,
                    '失败数': fail_stocks,
                    '炸板数': bomb_stocks,
                    '成功率': f"{success_rate:.1f}%",
                    '更新时间': data.get('update_time', '')
                })

            except Exception as e:
                print(f"❌ 处理 {filename} 失败: {e}")

    print(f"✅ 每日统计数据已导出到: {csv_filename}")

    # 导出详细股票数据
    csv_filename_detail = "dabanke_stocks_detail.csv"
    with open(csv_filename_detail, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['日期', '级别', '股票名称', '代码', '状态', '涨跌幅', '概念行业']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for filename in data_files:
            date_str = filename.replace("dabanke_", "").replace(".json", "")
            filepath = os.path.join(data_dir, filename)

            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                date_formatted = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:]}"

                # 导出每只股票的详细信息
                for level, stocks in data.get('stocks_by_level', {}).items():
                    for stock in stocks:
                        writer.writerow({
                            '日期': date_formatted,
                            '级别': level,
                            '股票名称': stock.get('name', ''),
                            '代码': stock.get('code', ''),
                            '状态': stock.get('status', ''),
                            '涨跌幅': stock.get('change_percent', ''),
                            '概念行业': stock.get('industry', '')
                        })

            except Exception as e:
                print(f"❌ 处理 {filename} 失败: {e}")

    print(f"✅ 详细股票数据已导出到: {csv_filename_detail}")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='大板客历史数据获取工具')
    parser.add_argument('--days', type=int, default=60, help='获取的交易日天数 (默认: 60)')
    parser.add_argument('--delay', type=float, default=2.0, help='请求间隔秒数 (默认: 2.0)')
    parser.add_argument('--force', action='store_true', help='强制重新获取已存在的数据')
    parser.add_argument('--analyze', action='store_true', help='只分析已有数据，不获取新数据')
    parser.add_argument('--export', action='store_true', help='导出数据到CSV文件')

    args = parser.parse_args()

    if args.export:
        export_to_csv()
    elif args.analyze:
        analyze_historical_data()
    else:
        get_historical_data(
            days=args.days,
            skip_existing=not args.force,
            delay_seconds=args.delay
        )

        # 获取完成后自动分析
        print("\n" + "="*80)
        analyze_historical_data()

if __name__ == "__main__":
    main()
