#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问财数据格式化显示 - 清晰版本
"""

import pywencai
import pandas as pd
import json
import os
from datetime import datetime
from market_info_sina import SinaMarketDataCollector

def extract_clean_data(data, data_type):
    """从问财返回的复杂数据中提取关键信息"""
    
    if data_type == "up_count":
        # 提取上涨家数
        if isinstance(data, dict):
            if 'table2' in data and data['table2']:
                return data['table2'][0].get('上涨家数', '未知')
            elif '涨跌家数分布' in data:
                for item in data['涨跌家数分布']:
                    if item.get('x') == '上涨':
                        return item.get('y', '未知')
        return '数据解析失败'
    
    elif data_type == "down_count":
        # 提取下跌家数
        if isinstance(data, dict):
            if '涨跌家数分布' in data:
                for item in data['涨跌家数分布']:
                    if item.get('x') == '下跌':
                        return item.get('y', '未知')
            elif '文本标题h1' in data:
                # 从文本中提取
                text = data['文本标题h1']
                if '下跌家数为' in text:
                    import re
                    match = re.search(r'下跌家数为(\d+)家', text)
                    if match:
                        return int(match.group(1))
        return '数据解析失败'
    return '未知'

def format_number(num):
    """格式化数字显示"""
    try:
        if isinstance(num, str):
            if num in ['数据解析失败', '未知']:
                return num
            num = float(num)

        if num >= 1e12:
            return f"{num/1e12:.2f}万亿"
        elif num >= 1e8:
            return f"{num/1e8:.2f}亿"
        elif num >= 1e4:
            return f"{num/1e4:.2f}万"
        else:
            return f"{num:.0f}"
    except:
        return str(num)

def save_market_data_json(market_data, limit_up_data, limit_down_data):
    """保存市场数据为JSON格式"""

    # 创建market_data文件夹
    os.makedirs('market_data', exist_ok=True)

    # 按日期命名文件
    today = datetime.now().strftime('%Y-%m-%d')
    filename = f'market_data/market_data_{today}.json'

    # 准备JSON数据
    json_data = {
        "date": today,
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "market_summary": {
            "up_count": market_data.get('up_count', 0),
            "down_count": market_data.get('down_count', 0),
            "total_amount": market_data.get('volume', 0),
            "total_amount_formatted": format_number(market_data.get('volume', 0))
        },
        "limit_stats": {
            "limit_up": {
                "total": market_data.get('limit_up_count', 0),
                "non_st": market_data.get('non_st_limit_up_count', 0),
                "st": market_data.get('st_limit_up_count', 0)
            },
            "limit_down": {
                "total": market_data.get('limit_down_count', 0),
                "non_st": market_data.get('non_st_limit_down_count', 0),
                "st": market_data.get('st_limit_down_count', 0)
            }
        },
        "limit_up_stocks": [],
        "limit_down_stocks": []
    }

    # 添加涨停股票详情
    if isinstance(limit_up_data, pd.DataFrame) and not limit_up_data.empty:
        for _, row in limit_up_data.iterrows():
            name = ""
            code = ""
            change = 0

            for col in limit_up_data.columns:
                if '股票简称' in col or '名称' in col:
                    name = row[col]
                elif '股票代码' in col or '代码' in col:
                    code = row[col]
                elif '涨跌幅' in col or '涨幅' in col:
                    try:
                        change = float(row[col])
                    except:
                        change = 0

            is_st = name and ('ST' in name.upper() or '*ST' in name.upper())

            json_data["limit_up_stocks"].append({
                "name": name,
                "code": code,
                "change_pct": change,
                "is_st": is_st
            })

    # 添加跌停股票详情
    if isinstance(limit_down_data, pd.DataFrame) and not limit_down_data.empty:
        for _, row in limit_down_data.iterrows():
            name = ""
            code = ""
            change = 0

            for col in limit_down_data.columns:
                if '股票简称' in col or '名称' in col:
                    name = row[col]
                elif '股票代码' in col or '代码' in col:
                    code = row[col]
                elif '涨跌幅' in col or '涨幅' in col:
                    try:
                        change = float(row[col])
                    except:
                        change = 0

            is_st = name and ('ST' in name.upper() or '*ST' in name.upper())

            json_data["limit_down_stocks"].append({
                "name": name,
                "code": code,
                "change_pct": change,
                "is_st": is_st
            })

    # 保存JSON文件
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        print(f"📄 市场数据已保存到: {filename}")
        return filename
    except Exception as e:
        print(f"❌ 保存JSON文件失败: {e}")
        return None

def get_formatted_market_data():
    """获取并格式化显示市场数据"""

    print("🚀 问财A股市场数据获取")
    print("=" * 60)

    # 初始化新浪数据收集器
    sina_collector = SinaMarketDataCollector()

    try:
        # 获取上涨家数
        print("📈 正在获取上涨家数...")
        up_data = pywencai.get(query='今天A股上涨家数', query_type='zhishu')
        up_count = extract_clean_data(up_data, "up_count")
        
        # 获取下跌家数
        print("📉 正在获取下跌家数...")
        down_data = pywencai.get(query='今天A股下跌家数', query_type='zhishu')
        down_count = extract_clean_data(down_data, "down_count")
        
        # 获取成交额（从新浪财经获取）
        print("💰 正在获取总成交额...")
        try:
            # 使用新浪财经数据收集器获取市场数据
            market_sentiment = sina_collector.get_market_sentiment(include_b_shares=False)
            if market_sentiment and 'total_amount' in market_sentiment:
                volume = market_sentiment['total_amount']
                print(f"   从新浪财经获取成交额: {format_number(volume)}")
            else:
                # 如果没有成交额数据，使用默认值
                print("新浪财经暂无成交额数据")
                volume = 0  # 
        except Exception as e:
            print(f"   新浪财经数据获取失败: {e}")
            volume = 0  # 
        
        # 获取涨跌停数据
        print("🎯 正在获取涨跌停数据...")
        limit_up = pywencai.get(query='今天涨停')
        limit_down = pywencai.get(query='今天跌停')

        limit_up_count = len(limit_up) if isinstance(limit_up, pd.DataFrame) else 0
        limit_down_count = len(limit_down) if isinstance(limit_down, pd.DataFrame) else 0

        # 分别统计ST和非ST的涨跌停
        st_limit_up_count = 0
        non_st_limit_up_count = 0
        st_limit_down_count = 0
        non_st_limit_down_count = 0

        # 统计涨停中的ST和非ST
        if isinstance(limit_up, pd.DataFrame) and not limit_up.empty:
            for _, row in limit_up.iterrows():
                name = ""
                for col in limit_up.columns:
                    if '股票简称' in col or '名称' in col:
                        name = row[col]
                        break

                if name and ('ST' in name.upper() or '*ST' in name.upper()):
                    st_limit_up_count += 1
                else:
                    non_st_limit_up_count += 1

        # 统计跌停中的ST和非ST
        if isinstance(limit_down, pd.DataFrame) and not limit_down.empty:
            for _, row in limit_down.iterrows():
                name = ""
                for col in limit_down.columns:
                    if '股票简称' in col or '名称' in col:
                        name = row[col]
                        break

                if name and ('ST' in name.upper() or '*ST' in name.upper()):
                    st_limit_down_count += 1
                else:
                    non_st_limit_down_count += 1
        
        # 格式化显示结果
        print("\n" + "=" * 60)
        print("📊 A股市场数据汇总")
        print("=" * 60)
        
        print(f"📅 数据时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 数据来源: 问财(同花顺)")
        print()
        
        # 基础市场数据
        print("📈 市场涨跌统计:")
        print(f"   上涨家数: {up_count}只")
        print(f"   下跌家数: {down_count}只")
        
        # 计算涨跌比例
        try:
            if isinstance(up_count, int) and isinstance(down_count, int):
                total = up_count + down_count
                if total > 0:
                    up_ratio = (up_count / total) * 100
                    down_ratio = (down_count / total) * 100
                    print(f"   涨跌比例: {up_ratio:.1f}% : {down_ratio:.1f}%")
                    
                    # 市场情绪判断
                    if up_ratio > 60:
                        mood = "🔥 强势上涨"
                    elif up_ratio > 50:
                        mood = "📈 偏强"
                    elif up_ratio > 40:
                        mood = "😐 震荡"
                    else:
                        mood = "📉 偏弱"
                    print(f"   市场情绪: {mood}")
        except:
            pass
        
        print()
        
        # 涨跌停统计
        print("🎯 涨跌停统计:")
        print(f"   涨停家数: {limit_up_count}只")
        print(f"     ├─ 非ST股票: {non_st_limit_up_count}只")
        print(f"     └─ ST股票: {st_limit_up_count}只")
        print(f"   跌停家数: {limit_down_count}只")
        print(f"     ├─ 非ST股票: {non_st_limit_down_count}只")
        print(f"     └─ ST股票: {st_limit_down_count}只")
        
        # 成交额
        print()
        print("💰 成交数据:")
        print(f"   总成交额: {format_number(volume)}")
        
        # 显示跌停股票详情（按ST和非ST分类）
        if isinstance(limit_down, pd.DataFrame) and not limit_down.empty:
            print()
            print("📉 跌停股票详情:")
            print("-" * 50)

            # 分类存储跌停股票
            non_st_stocks = []
            st_stocks = []

            for _, row in limit_down.iterrows():
                name = ""
                code = ""
                change = 0

                # 提取股票信息
                for col in limit_down.columns:
                    if '股票简称' in col or '名称' in col:
                        name = row[col]
                    elif '股票代码' in col or '代码' in col:
                        code = row[col]
                    elif '涨跌幅' in col or '涨幅' in col:
                        try:
                            change = float(row[col])
                        except:
                            change = 0

                stock_info = {'name': name, 'code': code, 'change': change}

                if name and ('ST' in name.upper() or '*ST' in name.upper()):
                    st_stocks.append(stock_info)
                else:
                    non_st_stocks.append(stock_info)

            # 显示非ST跌停股票
            if non_st_stocks:
                print("📊 非ST股票跌停:")
                for i, stock in enumerate(non_st_stocks, 1):
                    print(f"   {i:2d}. {stock['name']} ({stock['code']}) 跌幅: {stock['change']:.2f}%")

            # 显示ST跌停股票
            if st_stocks:
                print("⚠️  ST股票跌停:")
                for i, stock in enumerate(st_stocks, 1):
                    print(f"   {i:2d}. {stock['name']} ({stock['code']}) 跌幅: {stock['change']:.2f}%")
        
        # 显示部分涨停股票（按ST和非ST分类）
        if isinstance(limit_up, pd.DataFrame) and not limit_up.empty:
            print()
            print("📈 涨停股票详情:")
            print("-" * 50)

            # 分类存储涨停股票
            non_st_up_stocks = []
            st_up_stocks = []

            for _, row in limit_up.iterrows():
                name = ""
                code = ""
                change = 0

                for col in limit_up.columns:
                    if '股票简称' in col or '名称' in col:
                        name = row[col]
                    elif '股票代码' in col or '代码' in col:
                        code = row[col]
                    elif '涨跌幅' in col or '涨幅' in col:
                        try:
                            change = float(row[col])
                        except:
                            change = 0

                stock_info = {'name': name, 'code': code, 'change': change}

                if name and ('ST' in name.upper() or '*ST' in name.upper()):
                    st_up_stocks.append(stock_info)
                else:
                    non_st_up_stocks.append(stock_info)

            # 显示非ST涨停股票（前10只）
            if non_st_up_stocks:
                print("📊 非ST股票涨停(:")
                for i, stock in enumerate(non_st_up_stocks, 1):
                    print(f"   {i:2d}. {stock['name']} ({stock['code']}) 涨幅: {stock['change']:.2f}%")

            # 显示ST涨停股票
            if st_up_stocks:
                print("⚠️  ST股票涨停:")
                for i, stock in enumerate(st_up_stocks, 1):
                    print(f"   {i:2d}. {stock['name']} ({stock['code']}) 涨幅: {stock['change']:.2f}%")
        
        print()
        print("=" * 60)
        print("✅ 数据获取完成")
        
        # 保存数据
        today = datetime.now().strftime('%Y-%m-%d')

        # 保存JSON数据
        result_data = {
            'up_count': up_count,
            'down_count': down_count,
            'volume': volume,
            'limit_up_count': limit_up_count,
            'limit_down_count': limit_down_count,
            'non_st_limit_up_count': non_st_limit_up_count,
            'st_limit_up_count': st_limit_up_count,
            'non_st_limit_down_count': non_st_limit_down_count,
            'st_limit_down_count': st_limit_down_count,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        save_market_data_json(result_data, limit_up, limit_down)
        
        return {
            'up_count': up_count,
            'down_count': down_count,
            'volume': volume,
            'limit_up_count': limit_up_count,
            'limit_down_count': limit_down_count,
            'non_st_limit_up_count': non_st_limit_up_count,
            'st_limit_up_count': st_limit_up_count,
            'non_st_limit_down_count': non_st_limit_down_count,
            'st_limit_down_count': st_limit_down_count,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
    except Exception as e:
        print(f"❌ 数据获取失败: {e}")
        print("\n可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 稍后重试")
        print("3. 检查pywencai包版本")
        return None

if __name__ == "__main__":
    result = get_formatted_market_data()
    
    if result:
        print(f"\n📋 快速汇总:")
        print(f"上涨: {result['up_count']}只 | 下跌: {result['down_count']}只")
        print(f"涨停: {result['limit_up_count']}只 (非ST: {result['non_st_limit_up_count']}只, ST: {result['st_limit_up_count']}只)")
        print(f"跌停: {result['limit_down_count']}只 (非ST: {result['non_st_limit_down_count']}只, ST: {result['st_limit_down_count']}只)")
        print(f"成交额: {format_number(result['volume'])}")
        print(f"数据时间: {result['timestamp']}")
    else:
        print("数据获取失败，请检查网络连接或稍后重试")
