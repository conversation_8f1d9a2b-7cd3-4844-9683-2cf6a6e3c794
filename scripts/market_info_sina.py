#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用新浪财经API获取股票市场数据
更稳定的数据源，无需注册
"""

import requests
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional
import logging
import time
import re
import json

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SinaMarketDataCollector:
    """基于新浪财经API的市场数据收集器"""
    
    def __init__(self):
        self.today = datetime.now().strftime('%Y-%m-%d')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://finance.sina.com.cn/',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        })
        
    def get_all_stock_codes(self) -> List[str]:
        """获取所有A股代码（完整版本）"""
        codes = []

        # 上海主板 600000-605999 (扩大范围)
        for i in range(600000, 610000):
            codes.append(f"sh{i}")

        # 科创板 688000-689999 (包含CDR)
        for i in range(688000, 690000):
            codes.append(f"sh{i}")

        # 上海B股 900901-900999 (扩大范围)
        for i in range(900901, 901000):
            codes.append(f"sh{i}")

        # 深圳主板 000001-001999
        for i in range(1, 2000):
            codes.append(f"sz{i:06d}")

        # 深圳中小板 002001-002999 (现已并入主板)
        for i in range(2001, 3000):
            codes.append(f"sz{i:06d}")

        # 创业板 300001-301999 (扩大范围到包含301xxx)
        for i in range(300001, 302000):
            codes.append(f"sz{i}")

        # 深圳B股 200001-200999
        for i in range(200001, 201000):
            codes.append(f"sz{i}")

        # 北交所 - 获取更多范围
        # 8开头的北交所股票
        for i in range(800000, 900000, 50):  # 每50个取一个，增加密度
            codes.append(f"bj{i}")
        # 4开头的北交所股票
        for i in range(430000, 500000, 50):  # 每50个取一个，增加密度
            codes.append(f"bj{i}")

        return codes
    
    def get_realtime_data(self, codes: List[str], batch_size: int = 100) -> List[Dict]:
        """
        批量获取实时行情数据
        
        Args:
            codes: 股票代码列表
            batch_size: 每批请求的股票数量
            
        Returns:
            股票数据列表
        """
        all_data = []
        
        # 分批请求
        for i in range(0, len(codes), batch_size):
            batch_codes = codes[i:i + batch_size]
            code_str = ','.join(batch_codes)
            
            try:
                url = f"http://hq.sinajs.cn/list={code_str}"
                response = self.session.get(url, timeout=10)
                response.encoding = 'gbk'
                
                if response.status_code == 200:
                    # 解析返回数据
                    lines = response.text.strip().split('\n')
                    for line in lines:
                        if 'hq_str_' in line and '=""' not in line:
                            data = self._parse_sina_data(line)
                            if data:
                                all_data.append(data)
                
                # 避免请求过于频繁
                time.sleep(0.1)
                
            except Exception as e:
                logger.warning(f"批次 {i//batch_size + 1} 请求失败: {e}")
                continue
        
        return all_data
    
    def _parse_sina_data(self, line: str) -> Optional[Dict]:
        """解析新浪财经返回的数据行"""
        try:
            # 提取股票代码
            code_match = re.search(r'hq_str_(\w+)=', line)
            if not code_match:
                return None
            
            code = code_match.group(1)
            
            # 提取数据部分
            data_match = re.search(r'"([^"]+)"', line)
            if not data_match:
                return None
            
            data_str = data_match.group(1)
            if not data_str or data_str.count(',') < 10:
                return None
            
            parts = data_str.split(',')
            if len(parts) < 32:
                return None
            
            # 解析各字段
            name = parts[0]
            open_price = float(parts[1]) if parts[1] else 0
            yesterday_close = float(parts[2]) if parts[2] else 0
            current_price = float(parts[3]) if parts[3] else 0
            high_price = float(parts[4]) if parts[4] else 0
            low_price = float(parts[5]) if parts[5] else 0
            volume = int(parts[8]) if parts[8] else 0  # 成交量（手）
            amount = float(parts[9]) if parts[9] else 0  # 成交额（元）
            
            # 计算涨跌幅
            if yesterday_close > 0:
                change_pct = round((current_price - yesterday_close) / yesterday_close * 100, 2)
            else:
                change_pct = 0
            
            return {
                'code': code,
                'name': name,
                'current_price': current_price,
                'yesterday_close': yesterday_close,
                'change_pct': change_pct,
                'volume': volume,
                'amount': amount,
                'high': high_price,
                'low': low_price,
                'open': open_price
            }
            
        except Exception as e:
            logger.debug(f"解析数据失败: {e}, line: {line[:100]}")
            return None
    
    def is_st_stock(self, name: str) -> bool:
        """判断是否为ST股票"""
        if not name:
            return False
        name_upper = name.upper()
        st_keywords = ['ST', '*ST', 'SST', '*SST', 'S*ST', 'S', 'PT']
        return any(keyword in name_upper for keyword in st_keywords)

    def is_b_share(self, code: str) -> bool:
        """判断是否为B股"""
        if code.startswith('sh') and code[2:].startswith('900'):
            return True
        elif code.startswith('sz') and code[2:].startswith('200'):
            return True
        return False

    def is_delisted_stock(self, name: str, code: str) -> bool:
        """判断是否为退市股票"""
        if not name:
            return True

        name_upper = name.upper()

        # 明确的退市股票关键词（更精确）
        delisted_keywords = [
            '退市', 'DELISTED', '终止上市', '摘牌'
        ]

        # 退市整理期股票（以"退"结尾的股票名称）
        if name.endswith('退'):
            return True

        # 检查股票名称中是否包含明确的退市关键词
        for keyword in delisted_keywords:
            if keyword in name_upper:
                return True

        # 检查是否为空名称或异常名称
        if name.strip() == '' or name == 'N/A' or len(name.strip()) < 2:
            return True

        # 检查是否为测试股票
        if 'TEST' in name_upper or '测试' in name:
            return True

        return False

    def is_valid_trading_stock(self, stock_data: Dict) -> bool:
        """判断是否为有效的交易股票"""
        name = stock_data.get('name', '')
        code = stock_data.get('code', '')
        current_price = stock_data.get('current_price', 0)
        yesterday_close = stock_data.get('yesterday_close', 0)

        # 明确的退市股票检查
        if self.is_delisted_stock(name, code):
            return False

        # 对于有股票名称但价格为0的股票，可能是停牌股票，应该计入统计
        if name and name.strip() and current_price <= 0:
            # 如果有名称且不是明确的退市股票，认为是停牌股票，仍然有效
            return True

        # 基本数据检查 - 对于有价格的股票，必须有有效的价格数据
        if current_price <= 0 or yesterday_close <= 0:
            return False

        # 检查是否为正常的股票代码格式
        if code.startswith('sh') or code.startswith('sz'):
            code_num = code[2:]
            if code.startswith('sh'):
                # 上海市场 - 包含主板、科创板、B股
                if (code_num.startswith('60') or
                    code_num.startswith('688') or
                    code_num.startswith('689') or  # 科创板CDR
                    code_num.startswith('900')):
                    return True
                else:
                    return False
            elif code.startswith('sz'):
                # 深圳市场 - 包含主板、创业板、B股
                if (code_num.startswith('000') or
                    code_num.startswith('001') or
                    code_num.startswith('002') or
                    code_num.startswith('300') or
                    code_num.startswith('301') or  # 扩展的创业板
                    code_num.startswith('200')):
                    return True
                else:
                    return False
        elif code.startswith('bj'):
            # 北交所
            return True

        return False

    def classify_stock_by_code(self, code: str) -> str:
        """根据股票代码分类股票所属板块"""
        if code.startswith('sh'):
            num = code[2:]
            if num.startswith('60'):
                return '沪主板'
            elif num.startswith('688'):
                return '科创板'
            elif num.startswith('900'):
                return '沪B股'
        elif code.startswith('sz'):
            num = code[2:]
            if num.startswith('000') or num.startswith('001'):
                return '深主板'
            elif num.startswith('002'):
                return '深主板'  # 中小板已并入主板
            elif num.startswith('300'):
                return '创业板'
            elif num.startswith('200'):
                return '深B股'
        elif code.startswith('bj'):
            return '北交所'
        return '其他'

    def get_market_sentiment(self, include_b_shares: bool = True) -> Dict:
        """获取市场情绪数据（涨跌家数统计）"""
        logger.info("正在获取所有A股代码...")

        # 获取所有A股代码
        all_codes = self.get_all_stock_codes()
        logger.info(f"准备获取 {len(all_codes)} 只股票的实时数据...")

        # 获取实时数据
        stock_data = self.get_realtime_data(all_codes, batch_size=200)

        if not stock_data:
            logger.error("未获取到任何股票数据")
            return {}

        logger.info(f"成功获取 {len(stock_data)} 只股票的数据")

        # 过滤有效数据（排除停牌、退市等）
        valid_data = [s for s in stock_data if self.is_valid_trading_stock(s)]

        # 如果不包含B股，则过滤掉B股
        if not include_b_shares:
            valid_data = [s for s in valid_data if not self.is_b_share(s['code'])]

        if not valid_data:
            logger.error("没有有效的股票数据")
            return {}

        # 统计被过滤掉的股票类型
        filtered_out = len(stock_data) - len(valid_data)
        delisted_count = len([s for s in stock_data if self.is_delisted_stock(s.get('name', ''), s.get('code', ''))])
        low_price_count = len([s for s in stock_data if s.get('current_price', 0) < 0.1 and s.get('current_price', 0) > 0])

        logger.info(f"有效股票数据: {len(valid_data)} 只")
        logger.info(f"过滤掉: {filtered_out} 只 (其中退市股票: {delisted_count} 只, 低价股票: {low_price_count} 只)")

        # 分类统计：区分ST和非ST股票
        st_stocks = [s for s in valid_data if self.is_st_stock(s['name'])]
        non_st_stocks = [s for s in valid_data if not self.is_st_stock(s['name'])]

        logger.info(f"ST股票: {len(st_stocks)}只, 非ST股票: {len(non_st_stocks)}只")

        # 按板块分类统计
        board_stats = {}
        for stock in valid_data:
            board = self.classify_stock_by_code(stock['code'])
            if board not in board_stats:
                board_stats[board] = {'total': 0, 'up': 0, 'down': 0, 'flat': 0, 'limit_up': 0, 'limit_down': 0}

            board_stats[board]['total'] += 1
            if stock['change_pct'] > 0:
                board_stats[board]['up'] += 1
            elif stock['change_pct'] < 0:
                board_stats[board]['down'] += 1
            else:
                board_stats[board]['flat'] += 1

            if stock['change_pct'] >= 9.9:
                board_stats[board]['limit_up'] += 1
            elif stock['change_pct'] <= -9.9:
                board_stats[board]['limit_down'] += 1

        # 全市场统计
        def calculate_stats(stocks_list, prefix=""):
            up_count = len([s for s in stocks_list if s['change_pct'] > 0])
            down_count = len([s for s in stocks_list if s['change_pct'] < 0])
            flat_count = len([s for s in stocks_list if s['change_pct'] == 0])

            # 使用更严格的涨跌停标准
            limit_up_count = 0
            limit_down_count = 0

            for stock in stocks_list:
                change_pct = stock['change_pct']
                name = stock['name']
                current_price = stock['current_price']

                # 排除停牌股票（价格为0的股票）
                if current_price <= 0:
                    continue

                # 判断涨停（使用更宽松的标准）
                if self.is_st_stock(name):
                    # ST股票涨停标准：涨幅 >= 4.8%
                    if change_pct >= 4.8:
                        limit_up_count += 1
                else:
                    # 普通股票涨停标准：涨幅 >= 9.8%
                    if change_pct >= 9.8:
                        limit_up_count += 1

                # 判断跌停（使用更宽松的标准）
                if self.is_st_stock(name):
                    # ST股票跌停标准：跌幅 <= -4.7%
                    if change_pct <= -4.7:
                        limit_down_count += 1
                else:
                    # 普通股票跌停标准：跌幅 <= -9.8%
                    if change_pct <= -9.8:
                        limit_down_count += 1

            big_up_count = len([s for s in stocks_list if s['change_pct'] >= 5])
            big_down_count = len([s for s in stocks_list if s['change_pct'] <= -5])

            total = len(stocks_list)
            if total == 0:
                return {}

            return {
                f'{prefix}total_stocks': total,
                f'{prefix}up_count': up_count,
                f'{prefix}down_count': down_count,
                f'{prefix}flat_count': flat_count,
                f'{prefix}limit_up_count': limit_up_count,
                f'{prefix}limit_down_count': limit_down_count,
                f'{prefix}big_up_count': big_up_count,
                f'{prefix}big_down_count': big_down_count,
                f'{prefix}up_ratio': round(up_count / total * 100, 2),
                f'{prefix}down_ratio': round(down_count / total * 100, 2),
                f'{prefix}limit_up_ratio': round(limit_up_count / total * 100, 2),
                f'{prefix}limit_down_ratio': round(limit_down_count / total * 100, 2),
            }

        # 计算总成交量和成交额
        total_volume = sum(s['volume'] for s in valid_data)
        total_amount = sum(s['amount'] for s in valid_data)
        avg_change = sum(s['change_pct'] for s in valid_data) / len(valid_data)

        # 组合所有统计数据
        sentiment = {
            **calculate_stats(valid_data),  # 全市场
            **calculate_stats(non_st_stocks, 'non_st_'),  # 非ST股票
            **calculate_stats(st_stocks, 'st_'),  # ST股票
            'avg_change': round(avg_change, 2),
            'total_volume': total_volume,
            'total_amount': total_amount,
            'board_stats': board_stats,
            'date': self.today,
            'data_source': 'sina_finance'
        }

        logger.info(f"全市场统计完成: 总计{len(valid_data)}只(非ST:{len(non_st_stocks)}只, ST:{len(st_stocks)}只)")
        logger.info(f"涨跌情况: 上涨{sentiment['up_count']}只, 下跌{sentiment['down_count']}只")
        logger.info(f"涨跌停: 涨停{sentiment['limit_up_count']}只, 跌停{sentiment['limit_down_count']}只")

        return sentiment

    def analyze_filtered_stocks(self) -> Dict:
        """分析被过滤掉的股票，了解多算了哪些股票"""
        logger.info("正在分析被过滤的股票...")

        # 获取所有股票数据
        all_codes = self.get_all_stock_codes()
        stock_data = self.get_realtime_data(all_codes, batch_size=200)

        if not stock_data:
            return {}

        # 分类统计
        analysis = {
            'total_fetched': len(stock_data),
            'valid_stocks': 0,
            'delisted_stocks': [],
            'low_price_stocks': [],
            'invalid_data_stocks': [],
            'other_filtered': []
        }

        for stock in stock_data:
            name = stock.get('name', '')
            code = stock.get('code', '')
            price = stock.get('current_price', 0)

            if self.is_valid_trading_stock(stock):
                analysis['valid_stocks'] += 1
            else:
                # 分析被过滤的原因
                if self.is_delisted_stock(name, code):
                    analysis['delisted_stocks'].append({
                        'code': code,
                        'name': name,
                        'price': price,
                        'reason': '退市股票'
                    })
                elif price < 0.1 and price > 0:
                    analysis['low_price_stocks'].append({
                        'code': code,
                        'name': name,
                        'price': price,
                        'reason': '低价股票'
                    })
                elif price <= 0:
                    analysis['invalid_data_stocks'].append({
                        'code': code,
                        'name': name,
                        'price': price,
                        'reason': '无效价格数据'
                    })
                else:
                    analysis['other_filtered'].append({
                        'code': code,
                        'name': name,
                        'price': price,
                        'reason': '其他原因'
                    })

        # 统计各类数量
        analysis['delisted_count'] = len(analysis['delisted_stocks'])
        analysis['low_price_count'] = len(analysis['low_price_stocks'])
        analysis['invalid_data_count'] = len(analysis['invalid_data_stocks'])
        analysis['other_filtered_count'] = len(analysis['other_filtered'])
        analysis['total_filtered'] = (analysis['delisted_count'] +
                                    analysis['low_price_count'] +
                                    analysis['invalid_data_count'] +
                                    analysis['other_filtered_count'])

        logger.info(f"股票分析完成:")
        logger.info(f"  总获取: {analysis['total_fetched']} 只")
        logger.info(f"  有效股票: {analysis['valid_stocks']} 只")
        logger.info(f"  退市股票: {analysis['delisted_count']} 只")
        logger.info(f"  低价股票: {analysis['low_price_count']} 只")
        logger.info(f"  无效数据: {analysis['invalid_data_count']} 只")
        logger.info(f"  其他过滤: {analysis['other_filtered_count']} 只")

        return analysis

    def get_limit_up_down_details(self) -> Dict:
        """获取涨跌停股票详情"""
        logger.info("正在获取涨跌停股票详情...")

        # 使用与市场情绪统计相同的数据源
        all_codes = self.get_all_stock_codes()
        stock_data = self.get_realtime_data(all_codes, batch_size=200)

        # 使用相同的过滤逻辑
        valid_data = [s for s in stock_data if self.is_valid_trading_stock(s)]

        # 使用相同的涨跌停判断标准
        limit_up_stocks = []
        limit_down_stocks = []

        for stock in valid_data:
            change_pct = stock['change_pct']
            name = stock['name']
            current_price = stock['current_price']

            # 排除停牌股票（价格为0的股票）
            if current_price <= 0:
                continue

            # 判断涨停（使用更宽松的标准）
            if self.is_st_stock(name):
                # ST股票涨停标准：涨幅 >= 4.8%
                if change_pct >= 4.8:
                    limit_up_stocks.append(stock)
            else:
                # 普通股票涨停标准：涨幅 >= 9.8%
                if change_pct >= 9.8:
                    limit_up_stocks.append(stock)

            # 判断跌停（使用更宽松的标准）
            if self.is_st_stock(name):
                # ST股票跌停标准：跌幅 <= -4.7%
                if change_pct <= -4.7:
                    limit_down_stocks.append(stock)
            else:
                # 普通股票跌停标准：跌幅 <= -9.8%
                if change_pct <= -9.8:
                    limit_down_stocks.append(stock)

        return {
            'limit_up_count': len(limit_up_stocks),
            'limit_down_count': len(limit_down_stocks),
            'limit_up_details': limit_up_stocks[:20],  # 前20只
            'limit_down_details': limit_down_stocks[:20],  # 前20只
            'date': self.today
        }

    def get_market_summary(self) -> Dict:
        """获取市场总貌"""
        logger.info("正在获取市场总貌...")

        # 统计各板块股票数量（基于代码范围）
        summary = {
            'sse_summary': [
                {'项目': '上市股票', '股票': '约2300只', '说明': '上海证券交易所主板+科创板'},
                {'项目': '主板股票', '股票': '约1700只', '说明': '600000-603999'},
                {'项目': '科创板', '股票': '约600只', '说明': '688000-688999'},
            ],
            'szse_summary': [
                {'证券类别': '股票', '数量': '约2900只', '说明': '深圳证券交易所全部'},
                {'证券类别': '主板A股', '数量': '约1500只', '说明': '000001-002999'},
                {'证券类别': '创业板A股', '数量': '约1400只', '说明': '300001-300999'},
            ]
        }

        return summary

    def get_comprehensive_market_data(self, include_b_shares: bool = True) -> Dict:
        """获取综合市场数据"""
        logger.info("正在获取综合市场数据...")

        data = {
            'date': self.today,
            'timestamp': datetime.now().isoformat(),
        }

        # 获取市场情绪数据
        sentiment = self.get_market_sentiment(include_b_shares=include_b_shares)
        if sentiment:
            data['market_sentiment'] = sentiment

        # 获取涨跌停详情
        limit_details = self.get_limit_up_down_details()
        if limit_details:
            data['limit_up_down_stats'] = limit_details

        # 获取市场总貌
        summary = self.get_market_summary()
        if summary:
            data['market_summary'] = summary

        return data


def format_market_data(data: Dict) -> str:
    """格式化市场数据为可读字符串"""
    if not data:
        return "未获取到市场数据"

    result = []
    result.append(f"=== 股票市场数据 ({data.get('date', 'N/A')}) ===\n")

    # 市场情绪数据
    if 'market_sentiment' in data and data['market_sentiment']:
        sentiment = data['market_sentiment']

        # 全市场统计
        result.append("【全市场涨跌统计】")
        result.append(f"  总股票数: {sentiment.get('total_stocks', 'N/A')}只")
        result.append(f"  上涨家数: {sentiment.get('up_count', 'N/A')}只 ({sentiment.get('up_ratio', 'N/A')}%)")
        result.append(f"  下跌家数: {sentiment.get('down_count', 'N/A')}只 ({sentiment.get('down_ratio', 'N/A')}%)")
        result.append(f"  平盘家数: {sentiment.get('flat_count', 'N/A')}只")
        result.append(f"  涨停家数: {sentiment.get('limit_up_count', 'N/A')}只 ({sentiment.get('limit_up_ratio', 'N/A')}%)")
        result.append(f"  跌停家数: {sentiment.get('limit_down_count', 'N/A')}只 ({sentiment.get('limit_down_ratio', 'N/A')}%)")
        result.append(f"  大涨家数(≥5%): {sentiment.get('big_up_count', 'N/A')}只")
        result.append(f"  大跌家数(≤-5%): {sentiment.get('big_down_count', 'N/A')}只")
        result.append(f"  平均涨跌幅: {sentiment.get('avg_change', 'N/A')}%")
        result.append("")

        # 非ST股票统计
        if sentiment.get('non_st_total_stocks', 0) > 0:
            result.append("【非ST股票统计】")
            result.append(f"  总股票数: {sentiment.get('non_st_total_stocks', 'N/A')}只")
            result.append(f"  上涨家数: {sentiment.get('non_st_up_count', 'N/A')}只 ({sentiment.get('non_st_up_ratio', 'N/A')}%)")
            result.append(f"  下跌家数: {sentiment.get('non_st_down_count', 'N/A')}只 ({sentiment.get('non_st_down_ratio', 'N/A')}%)")
            result.append(f"  平盘家数: {sentiment.get('non_st_flat_count', 'N/A')}只")
            result.append(f"  涨停家数: {sentiment.get('non_st_limit_up_count', 'N/A')}只 ({sentiment.get('non_st_limit_up_ratio', 'N/A')}%)")
            result.append(f"  跌停家数: {sentiment.get('non_st_limit_down_count', 'N/A')}只 ({sentiment.get('non_st_limit_down_ratio', 'N/A')}%)")
            result.append("")

        # ST股票统计
        if sentiment.get('st_total_stocks', 0) > 0:
            result.append("【ST股票统计】")
            result.append(f"  总股票数: {sentiment.get('st_total_stocks', 'N/A')}只")
            result.append(f"  上涨家数: {sentiment.get('st_up_count', 'N/A')}只 ({sentiment.get('st_up_ratio', 'N/A')}%)")
            result.append(f"  下跌家数: {sentiment.get('st_down_count', 'N/A')}只 ({sentiment.get('st_down_ratio', 'N/A')}%)")
            result.append(f"  平盘家数: {sentiment.get('st_flat_count', 'N/A')}只")
            result.append(f"  涨停家数: {sentiment.get('st_limit_up_count', 'N/A')}只 ({sentiment.get('st_limit_up_ratio', 'N/A')}%)")
            result.append(f"  跌停家数: {sentiment.get('st_limit_down_count', 'N/A')}只 ({sentiment.get('st_limit_down_ratio', 'N/A')}%)")
            result.append("")

        # 分板块统计
        if sentiment.get('board_stats'):
            result.append("【分板块统计】")
            board_stats = sentiment['board_stats']
            for board, stats in board_stats.items():
                if stats['total'] > 0:
                    result.append(f"  {board}: 总计{stats['total']}只")
                    result.append(f"    上涨{stats['up']}只, 下跌{stats['down']}只, 平盘{stats['flat']}只")
                    result.append(f"    涨停{stats['limit_up']}只, 跌停{stats['limit_down']}只")
            result.append("")

        result.append("【成交数据】")
        result.append(f"  总成交量: {sentiment.get('total_volume', 0):,}手")
        result.append(f"  总成交额: {sentiment.get('total_amount', 0):,.2f}元")
        result.append(f"  数据来源: {sentiment.get('data_source', 'N/A')}")
        result.append("")

    # 涨跌停详细统计
    if 'limit_up_down_stats' in data and data['limit_up_down_stats']:
        stats = data['limit_up_down_stats']
        result.append("【涨跌停详情】")
        result.append(f"  涨停家数: {stats.get('limit_up_count', 'N/A')}只")
        result.append(f"  跌停家数: {stats.get('limit_down_count', 'N/A')}只")

        # 显示部分涨停股
        if stats.get('limit_up_details'):
            result.append("  涨停股示例(前10只):")
            for i, stock in enumerate(stats['limit_up_details'][:10], 1):
                result.append(f"    {i}. {stock.get('name', 'N/A')} ({stock.get('code', 'N/A')}) "
                            f"涨幅: {stock.get('change_pct', 'N/A')}%")

        # 显示部分跌停股
        if stats.get('limit_down_details'):
            result.append("  跌停股示例(前10只):")
            for i, stock in enumerate(stats['limit_down_details'][:10], 1):
                result.append(f"    {i}. {stock.get('name', 'N/A')} ({stock.get('code', 'N/A')}) "
                            f"跌幅: {stock.get('change_pct', 'N/A')}%")
        result.append("")

    # 市场总貌
    if 'market_summary' in data and data['market_summary']:
        summary = data['market_summary']
        result.append("【市场总貌】")

        # 上交所数据
        if summary.get('sse_summary'):
            result.append("  上海证券交易所:")
            for item in summary['sse_summary']:
                result.append(f"    {item.get('项目', 'N/A')}: {item.get('股票', 'N/A')}")

        # 深交所数据
        if summary.get('szse_summary'):
            result.append("  深圳证券交易所:")
            for item in summary['szse_summary']:
                result.append(f"    {item.get('证券类别', 'N/A')}: {item.get('数量', 'N/A')}")
        result.append("")

    result.append(f"数据获取时间: {data.get('timestamp', 'N/A')}")

    return "\n".join(result)


def save_to_csv(data: Dict, filename: str = None):
    """将市场数据保存为CSV文件"""
    if not data:
        logger.warning("没有数据可保存")
        return

    if filename is None:
        date_str = data.get('date', datetime.now().strftime('%Y-%m-%d'))
        filename = f"sina_market_data_{date_str}.csv"

    try:
        rows = []

        # 市场情绪数据
        if 'market_sentiment' in data and data['market_sentiment']:
            sentiment = data['market_sentiment']

            # 全市场统计
            all_market_metrics = [
                ('全市场_总股票数', sentiment.get('total_stocks', 0)),
                ('全市场_上涨家数', sentiment.get('up_count', 0)),
                ('全市场_下跌家数', sentiment.get('down_count', 0)),
                ('全市场_平盘家数', sentiment.get('flat_count', 0)),
                ('全市场_涨停家数', sentiment.get('limit_up_count', 0)),
                ('全市场_跌停家数', sentiment.get('limit_down_count', 0)),
                ('全市场_大涨家数(≥5%)', sentiment.get('big_up_count', 0)),
                ('全市场_大跌家数(≤-5%)', sentiment.get('big_down_count', 0)),
                ('全市场_上涨比例(%)', sentiment.get('up_ratio', 0)),
                ('全市场_下跌比例(%)', sentiment.get('down_ratio', 0)),
                ('全市场_涨停比例(%)', sentiment.get('limit_up_ratio', 0)),
                ('全市场_跌停比例(%)', sentiment.get('limit_down_ratio', 0)),
                ('全市场_平均涨跌幅(%)', sentiment.get('avg_change', 0)),
                ('总成交量(手)', sentiment.get('total_volume', 0)),
                ('总成交额(元)', sentiment.get('total_amount', 0))
            ]

            # 非ST股票统计
            non_st_metrics = [
                ('非ST_总股票数', sentiment.get('non_st_total_stocks', 0)),
                ('非ST_上涨家数', sentiment.get('non_st_up_count', 0)),
                ('非ST_下跌家数', sentiment.get('non_st_down_count', 0)),
                ('非ST_平盘家数', sentiment.get('non_st_flat_count', 0)),
                ('非ST_涨停家数', sentiment.get('non_st_limit_up_count', 0)),
                ('非ST_跌停家数', sentiment.get('non_st_limit_down_count', 0)),
                ('非ST_上涨比例(%)', sentiment.get('non_st_up_ratio', 0)),
                ('非ST_下跌比例(%)', sentiment.get('non_st_down_ratio', 0)),
                ('非ST_涨停比例(%)', sentiment.get('non_st_limit_up_ratio', 0)),
                ('非ST_跌停比例(%)', sentiment.get('non_st_limit_down_ratio', 0)),
            ]

            # ST股票统计
            st_metrics = [
                ('ST_总股票数', sentiment.get('st_total_stocks', 0)),
                ('ST_上涨家数', sentiment.get('st_up_count', 0)),
                ('ST_下跌家数', sentiment.get('st_down_count', 0)),
                ('ST_平盘家数', sentiment.get('st_flat_count', 0)),
                ('ST_涨停家数', sentiment.get('st_limit_up_count', 0)),
                ('ST_跌停家数', sentiment.get('st_limit_down_count', 0)),
                ('ST_上涨比例(%)', sentiment.get('st_up_ratio', 0)),
                ('ST_下跌比例(%)', sentiment.get('st_down_ratio', 0)),
                ('ST_涨停比例(%)', sentiment.get('st_limit_up_ratio', 0)),
                ('ST_跌停比例(%)', sentiment.get('st_limit_down_ratio', 0)),
            ]

            # 添加所有指标到rows
            for metric_name, value in all_market_metrics + non_st_metrics + st_metrics:
                rows.append({
                    'date': data['date'],
                    'category': '市场统计',
                    'metric': metric_name,
                    'value': value,
                    'data_source': sentiment.get('data_source', 'sina_finance')
                })

            # 分板块统计
            if sentiment.get('board_stats'):
                for board, stats in sentiment['board_stats'].items():
                    board_metrics = [
                        (f'{board}_总股票数', stats['total']),
                        (f'{board}_上涨家数', stats['up']),
                        (f'{board}_下跌家数', stats['down']),
                        (f'{board}_平盘家数', stats['flat']),
                        (f'{board}_涨停家数', stats['limit_up']),
                        (f'{board}_跌停家数', stats['limit_down']),
                    ]

                    for metric_name, value in board_metrics:
                        rows.append({
                            'date': data['date'],
                            'category': '分板块统计',
                            'metric': metric_name,
                            'value': value,
                            'data_source': sentiment.get('data_source', 'sina_finance')
                        })

        # 保存为CSV
        if rows:
            df = pd.DataFrame(rows)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            logger.info(f"数据已保存到 {filename}")
        else:
            logger.warning("没有有效数据可保存")

    except Exception as e:
        logger.error(f"保存CSV文件失败: {e}")


def main():
    """主函数"""
    # 创建新浪财经数据收集器
    collector = SinaMarketDataCollector()

    # 获取综合市场数据
    print("正在获取股票市场数据...")
    print("注意：可以通过修改 include_b_shares 参数来选择是否包含B股")
    print("当前设置：包含B股")
    data = collector.get_comprehensive_market_data()

    if data:
        # 打印格式化的数据
        print(format_market_data(data))

        # 保存为CSV文件
        save_to_csv(data)

        # 保存为JSON文件
        import json
        date_str = data.get('date', datetime.now().strftime('%Y-%m-%d'))
        json_filename = f"sina_market_data_{date_str}.json"
        try:
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"\n数据已保存到 {json_filename}")
        except Exception as e:
            logger.error(f"保存JSON文件失败: {e}")
    else:
        print("未能获取到市场数据，请检查网络连接或稍后重试")


if __name__ == "__main__":
    main()
