#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试定时调度脚本的时间逻辑
"""

import datetime
from stock_data_scheduler import StockDataScheduler

def test_time_logic():
    """测试时间逻辑"""
    scheduler = StockDataScheduler()
    
    print("🧪 测试A股交易时间判断逻辑")
    print("="*50)
    
    # 测试用例：不同时间点
    test_cases = [
        # 工作日测试
        datetime.datetime(2024, 1, 15, 9, 0),   # 周一 9:00 - 开盘前
        datetime.datetime(2024, 1, 15, 9, 30),  # 周一 9:30 - 开盘
        datetime.datetime(2024, 1, 15, 10, 30), # 周一 10:30 - 交易中
        datetime.datetime(2024, 1, 15, 11, 30), # 周一 11:30 - 上午收盘
        datetime.datetime(2024, 1, 15, 12, 0),  # 周一 12:00 - 午休
        datetime.datetime(2024, 1, 15, 13, 0),  # 周一 13:00 - 下午开盘
        datetime.datetime(2024, 1, 15, 14, 30), # 周一 14:30 - 交易中
        datetime.datetime(2024, 1, 15, 15, 0),  # 周一 15:00 - 收盘
        datetime.datetime(2024, 1, 15, 16, 0),  # 周一 16:00 - 收盘后
        
        # 周末测试
        datetime.datetime(2024, 1, 13, 10, 30), # 周六 10:30
        datetime.datetime(2024, 1, 14, 14, 30), # 周日 14:30
    ]
    
    for test_time in test_cases:
        is_weekday = scheduler.is_weekday(test_time)
        is_trading = scheduler.is_trading_time(test_time)
        weekday_name = test_time.strftime("%A")
        time_str = test_time.strftime("%H:%M")
        
        status = "🟢 交易时间" if is_trading else "🔴 非交易时间"
        day_status = "工作日" if is_weekday else "周末"
        
        print(f"{weekday_name} {time_str} - {day_status} - {status}")
    
    print("\n" + "="*50)

def test_execution_intervals():
    """测试执行间隔逻辑"""
    scheduler = StockDataScheduler()
    
    print("🧪 测试执行间隔逻辑")
    print("="*50)
    
    # 模拟不同的执行场景
    base_time = datetime.datetime(2024, 1, 15, 10, 0)  # 周一 10:00 交易时间
    
    # 场景1：首次执行
    print("场景1：首次执行")
    should_exec = scheduler.should_execute(base_time)
    print(f"   应该执行: {should_exec} (首次执行应该为True)")
    
    # 场景2：交易时间，间隔4分钟
    print("\n场景2：交易时间，间隔4分钟")
    scheduler.last_execution_time = base_time
    test_time = base_time + datetime.timedelta(minutes=4)
    should_exec = scheduler.should_execute(test_time)
    print(f"   应该执行: {should_exec} (4分钟间隔，应该为False)")
    
    # 场景3：交易时间，间隔5分钟
    print("\n场景3：交易时间，间隔5分钟")
    test_time = base_time + datetime.timedelta(minutes=5)
    should_exec = scheduler.should_execute(test_time)
    print(f"   应该执行: {should_exec} (5分钟间隔，应该为True)")
    
    # 场景4：非交易时间，间隔29分钟
    print("\n场景4：非交易时间，间隔29分钟")
    non_trading_time = datetime.datetime(2024, 1, 15, 16, 0)  # 收盘后
    scheduler.last_execution_time = non_trading_time
    test_time = non_trading_time + datetime.timedelta(minutes=29)
    should_exec = scheduler.should_execute(test_time)
    print(f"   应该执行: {should_exec} (29分钟间隔，应该为False)")
    
    # 场景5：非交易时间，间隔30分钟
    print("\n场景5：非交易时间，间隔30分钟")
    test_time = non_trading_time + datetime.timedelta(minutes=30)
    should_exec = scheduler.should_execute(test_time)
    print(f"   应该执行: {should_exec} (30分钟间隔，应该为True)")
    
    print("\n" + "="*50)

def test_next_execution_info():
    """测试下次执行时间信息"""
    scheduler = StockDataScheduler()

    print("🧪 测试下次执行时间信息")
    print("="*50)

    # 交易时间测试
    trading_time = datetime.datetime(2024, 1, 15, 10, 30)  # 周一 10:30

    # 直接测试交易时间的下次执行逻辑
    if scheduler.is_trading_time(trading_time):
        next_time = trading_time + datetime.timedelta(minutes=5)
        interval = "5分钟"
    else:
        next_time = trading_time + datetime.timedelta(minutes=30)
        interval = "30分钟"

    print(f"交易时间 {trading_time.strftime('%H:%M')}:")
    print(f"   下次执行: {next_time.strftime('%H:%M')}")
    print(f"   间隔: {interval}")

    # 非交易时间测试
    non_trading_time = datetime.datetime(2024, 1, 15, 16, 0)  # 收盘后

    if scheduler.is_trading_time(non_trading_time):
        next_time = non_trading_time + datetime.timedelta(minutes=5)
        interval = "5分钟"
    else:
        next_time = non_trading_time + datetime.timedelta(minutes=30)
        interval = "30分钟"

    print(f"\n非交易时间 {non_trading_time.strftime('%H:%M')}:")
    print(f"   下次执行: {next_time.strftime('%H:%M')}")
    print(f"   间隔: {interval}")

    print("\n" + "="*50)

def main():
    """主函数"""
    print("🧪 A股数据定时采集脚本 - 逻辑测试")
    print("="*60)
    
    test_time_logic()
    print()
    test_execution_intervals()
    print()
    test_next_execution_info()
    
    print("✅ 所有测试完成")

if __name__ == "__main__":
    main()
