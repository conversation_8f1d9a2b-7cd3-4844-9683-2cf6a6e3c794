#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速获取大板客最近60天数据的脚本
"""

import subprocess
import sys
import time

def main():
    """获取最近60天的数据"""
    print("🚀 开始获取大板客最近60个交易日的数据")
    print("⏰ 预计需要约5-8分钟完成（包含请求延迟）")
    print("="*80)
    
    try:
        # 运行历史数据获取脚本
        cmd = [
            sys.executable, 
            "dabanke_historical.py", 
            "--days", "60",
            "--delay", "2.5"  # 2.5秒延迟，避免请求过于频繁
        ]
        
        print("📊 执行命令:", " ".join(cmd))
        print()
        
        # 执行命令
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            print("\n" + "="*80)
            print("🎉 60天数据获取完成！")
            print("📁 数据保存在 dabanke_data/ 目录下")
            print("📊 每个文件格式: dabanke_YYYYMMDD.json")
            print()
            
            # 导出CSV文件
            print("📈 正在导出CSV文件...")
            csv_cmd = [sys.executable, "dabanke_historical.py", "--export"]
            csv_result = subprocess.run(csv_cmd, capture_output=False, text=True)
            
            if csv_result.returncode == 0:
                print("✅ CSV文件导出完成")
                print("📊 dabanke_daily_stats.csv - 每日统计数据")
                print("📋 dabanke_stocks_detail.csv - 详细股票数据")
            
            print("\n🎯 使用建议:")
            print("1. 查看每日统计: python dabanke_historical.py --analyze")
            print("2. 重新导出CSV: python dabanke_historical.py --export")
            print("3. 获取更多数据: python dabanke_historical.py --days 90")
            print("4. 强制更新: python dabanke_historical.py --days 60 --force")
            
        else:
            print("❌ 数据获取失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了数据获取过程")
        print("💡 已获取的数据仍然保存在 dabanke_data/ 目录下")
        print("💡 可以重新运行脚本继续获取剩余数据")
        
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {e}")

if __name__ == "__main__":
    main()
