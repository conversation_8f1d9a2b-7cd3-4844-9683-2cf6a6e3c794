#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新浪财经API使用示例
演示如何获取股票市场的涨跌家数、涨跌停家数等数据
"""

from market_info_sina import SinaMarketDataCollector, format_market_data, save_to_csv
import json

def main():
    """主函数，演示各种数据获取功能"""
    
    # 创建新浪财经数据收集器
    collector = SinaMarketDataCollector()
    
    print("=== 新浪财经API股票市场数据获取示例 ===\n")
    
    # 1. 获取市场情绪数据（涨跌家数统计）
    print("1. 获取全市场涨跌统计...")
    sentiment = collector.get_market_sentiment()
    if sentiment:
        print(f"   【全市场统计】")
        print(f"   总股票数: {sentiment['total_stocks']}只")
        print(f"   上涨家数: {sentiment['up_count']}只 ({sentiment['up_ratio']}%)")
        print(f"   下跌家数: {sentiment['down_count']}只 ({sentiment['down_ratio']}%)")
        print(f"   平盘家数: {sentiment['flat_count']}只")
        print(f"   涨停家数: {sentiment['limit_up_count']}只 ({sentiment['limit_up_ratio']}%)")
        print(f"   跌停家数: {sentiment['limit_down_count']}只 ({sentiment['limit_down_ratio']}%)")
        print(f"   大涨家数(≥5%): {sentiment['big_up_count']}只")
        print(f"   大跌家数(≤-5%): {sentiment['big_down_count']}只")
        print(f"   平均涨跌幅: {sentiment['avg_change']}%")

        print(f"\n   【非ST股票统计】")
        print(f"   总股票数: {sentiment.get('non_st_total_stocks', 0)}只")
        print(f"   上涨家数: {sentiment.get('non_st_up_count', 0)}只 ({sentiment.get('non_st_up_ratio', 0)}%)")
        print(f"   下跌家数: {sentiment.get('non_st_down_count', 0)}只 ({sentiment.get('non_st_down_ratio', 0)}%)")
        print(f"   涨停家数: {sentiment.get('non_st_limit_up_count', 0)}只 ({sentiment.get('non_st_limit_up_ratio', 0)}%)")
        print(f"   跌停家数: {sentiment.get('non_st_limit_down_count', 0)}只 ({sentiment.get('non_st_limit_down_ratio', 0)}%)")

        print(f"\n   【ST股票统计】")
        print(f"   总股票数: {sentiment.get('st_total_stocks', 0)}只")
        print(f"   上涨家数: {sentiment.get('st_up_count', 0)}只 ({sentiment.get('st_up_ratio', 0)}%)")
        print(f"   下跌家数: {sentiment.get('st_down_count', 0)}只 ({sentiment.get('st_down_ratio', 0)}%)")
        print(f"   涨停家数: {sentiment.get('st_limit_up_count', 0)}只 ({sentiment.get('st_limit_up_ratio', 0)}%)")
        print(f"   跌停家数: {sentiment.get('st_limit_down_count', 0)}只 ({sentiment.get('st_limit_down_ratio', 0)}%)")

        print(f"\n   【成交数据】")
        print(f"   总成交量: {sentiment['total_volume']:,}手")
        print(f"   总成交额: {sentiment['total_amount']:,.2f}元")
        print(f"   数据来源: {sentiment['data_source']}")

        # 显示分板块统计
        if sentiment.get('board_stats'):
            print(f"\n   【分板块统计】")
            for board, stats in sentiment['board_stats'].items():
                if stats['total'] > 0:
                    print(f"   {board}: 总计{stats['total']}只, 上涨{stats['up']}只, 下跌{stats['down']}只, 涨停{stats['limit_up']}只, 跌停{stats['limit_down']}只")
    print()
    
    # 2. 获取涨跌停详情
    print("2. 获取涨跌停详情...")
    limit_details = collector.get_limit_up_down_details()
    if limit_details:
        print(f"   涨停家数: {limit_details['limit_up_count']}只")
        print(f"   跌停家数: {limit_details['limit_down_count']}只")
        
        # 显示涨停股示例
        if limit_details.get('limit_up_details'):
            print("   涨停股示例(前5只):")
            for i, stock in enumerate(limit_details['limit_up_details'][:5], 1):
                print(f"     {i}. {stock['name']} ({stock['code']}) "
                      f"涨幅: {stock['change_pct']}% "
                      f"价格: {stock['current_price']}元")
        
        # 显示跌停股示例
        if limit_details.get('limit_down_details'):
            print("   跌停股示例(前5只):")
            for i, stock in enumerate(limit_details['limit_down_details'][:5], 1):
                print(f"     {i}. {stock['name']} ({stock['code']}) "
                      f"跌幅: {stock['change_pct']}% "
                      f"价格: {stock['current_price']}元")
    print()
    
    # 3. 获取市场总貌
    print("3. 获取市场总貌...")
    summary = collector.get_market_summary()
    if summary:
        print("   上海证券交易所:")
        for item in summary['sse_summary']:
            print(f"     {item['项目']}: {item['股票']}")
        
        print("   深圳证券交易所:")
        for item in summary['szse_summary']:
            print(f"     {item['证券类别']}: {item['数量']}")
    print()
    
    # 4. 获取综合数据并保存
    print("4. 获取综合数据并保存...")
    comprehensive_data = collector.get_comprehensive_market_data()
    if comprehensive_data:
        # 保存为JSON
        with open('sina_market_example.json', 'w', encoding='utf-8') as f:
            json.dump(comprehensive_data, f, ensure_ascii=False, indent=2)
        
        # 保存为CSV
        save_to_csv(comprehensive_data, 'sina_market_example.csv')
        
        print("   数据已保存到 sina_market_example.json 和 sina_market_example.csv")
        
        # 显示格式化的完整报告
        print("\n" + "="*60)
        print("完整市场报告:")
        print("="*60)
        print(format_market_data(comprehensive_data))
    
    print("\n=== 数据获取完成 ===")
    print("\n优势说明:")
    print("✅ 使用新浪财经API，稳定可靠")
    print("✅ 无需注册，免费使用")
    print("✅ 实时数据，准确性高")
    print("✅ 包含完整的涨跌家数和涨跌停统计")
    print("✅ 支持CSV和JSON格式导出")

if __name__ == "__main__":
    main()
