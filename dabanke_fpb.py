#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大板客网站连板雁阵图数据提取脚本 - 复盘宝版本
使用 https://dabanke.com/fpb.html 页面，数据结构更清晰
"""

import requests
import re
from datetime import datetime
import json

def get_dabanke_fpb_data():
    """获取大板客复盘宝页面数据"""
    url = "https://dabanke.com/fpb.html"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        response.encoding = 'utf-8'
        return response.text
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return None

def extract_lianban_data_from_fpb(html_content):
    """从复盘宝页面提取连板雁阵图数据"""
    data = {
        'update_time': '',
        'stocks_by_level': {},
        'concept_stats': [],
        'market_overview': {},
        'longhu_summary': []
    }
    
    # 提取更新时间
    time_match = re.search(r'最后更新时间:\s*(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', html_content)
    if time_match:
        data['update_time'] = time_match.group(1)
    
    # 提取连板雁阵图数据
    data['stocks_by_level'] = extract_stocks_by_level_fpb(html_content)
    
    # 提取概念统计
    data['concept_stats'] = extract_concept_stats_fpb(html_content)
    
    # 提取市场总览
    data['market_overview'] = extract_market_overview_fpb(html_content)
    
    # 提取龙虎榜摘要
    data['longhu_summary'] = extract_longhu_summary_fpb(html_content)
    
    return data

def extract_stocks_by_level_fpb(html_content):
    """从复盘宝页面提取按级别分类的股票数据"""
    stocks_by_level = {
        '5进6': [],
        '4进5': [],
        '2进3': [],
        '1进2': [],
        '首板': []
    }

    # 直接从HTML文本中提取，使用更简单的方法
    # 先找到所有股票信息
    stock_pattern = r'([深沪科创])\s+\[([^\]]+)\]\(/gupiao-(\d+)\.html\)\s+\(([成败炸])\)\s+([-+]?\d+\.?\d*)\s*%\s+\(([^)]+)\)'
    all_stocks = re.findall(stock_pattern, html_content)

    # 查找雁阵图部分
    yanzheng_section = re.search(r'涨停连板 雁阵图.*?(?=各概念下涨停股票详情|$)', html_content, re.DOTALL)
    if not yanzheng_section:
        return stocks_by_level

    yanzheng_content = yanzheng_section.group()

    # 按位置分配股票到不同级别
    # 根据观察，股票在HTML中的出现顺序对应级别
    stock_index = 0

    # 查找级别标记的位置
    level_positions = []
    for match in re.finditer(r'<h\d[^>]*>(\d+)</h\d>', yanzheng_content):
        level_num = match.group(1)
        position = match.start()
        level_positions.append((position, level_num))

    # 如果没有找到h标签，尝试其他方式
    if not level_positions:
        lines = yanzheng_content.split('\n')
        for i, line in enumerate(lines):
            line = line.strip()
            if re.match(r'^\d+$', line):
                level_num = line
                level_positions.append((i * 100, level_num))  # 使用行号作为位置

    # 为每个找到的股票分配级别
    for market, name, code, status, percent, concept in all_stocks:
        # 根据股票在HTML中的位置确定级别
        stock_pos = yanzheng_content.find(f'[{name}]')

        if stock_pos == -1:
            continue

        # 找到最接近的级别
        assigned_level = None
        for pos, level_num in level_positions:
            if stock_pos > pos:
                if level_num == '6':
                    assigned_level = '5进6'
                elif level_num == '5':
                    assigned_level = '4进5'
                elif level_num == '3':
                    assigned_level = '2进3'
                elif level_num == '2':
                    assigned_level = '1进2'
                elif level_num == '1':
                    assigned_level = '首板'

        # 如果没有找到合适的级别，根据顺序分配
        if not assigned_level:
            if stock_index < 5:
                assigned_level = '5进6'
            elif stock_index < 15:
                assigned_level = '4进5'
            elif stock_index < 25:
                assigned_level = '2进3'
            elif stock_index < 50:
                assigned_level = '1进2'
            else:
                assigned_level = '首板'

        if assigned_level:
            status_emoji = {"成": "✅", "败": "❌", "炸": "💥"}.get(status, "")

            stock_info = {
                'market': market,
                'name': name.strip(),
                'code': code,
                'status': status,
                'status_emoji': status_emoji,
                'change_percent': percent,
                'concept': concept.strip(),
                'level': assigned_level
            }

            stocks_by_level[assigned_level].append(stock_info)

        stock_index += 1

    return stocks_by_level

def extract_concept_stats_fpb(html_content):
    """提取概念统计数据"""
    concept_stats = []
    
    # 查找概念统计部分
    concept_section = re.search(r'各概念下涨停股票详情.*?(?=下表是短线市场总览|$)', html_content, re.DOTALL)
    if not concept_section:
        return concept_stats
    
    content = concept_section.group()
    
    # 匹配概念统计格式：机器人概念 9/18 [股票列表]
    concept_pattern = r'([^/\n]+?)\s+(\d+/\d+)\s+\[([^\]]+)\]'
    matches = re.findall(concept_pattern, content)
    
    for match in matches:
        concept_name, ratio, stocks_text = match
        
        # 提取股票列表
        stock_links = re.findall(r'\[([^\]]+)\]\(/gupiao-(\d+)\.html\)', stocks_text)
        stocks = [{'name': name, 'code': code} for name, code in stock_links]
        
        concept_stats.append({
            'concept': concept_name.strip(),
            'ratio': ratio,
            'success_rate': calculate_success_rate(ratio),
            'stocks': stocks
        })
    
    return concept_stats

def extract_market_overview_fpb(html_content):
    """提取市场总览数据"""
    market_overview = {}
    
    # 查找市场总览部分
    overview_section = re.search(r'短线市场总览.*?(?=下表是10个交易日|$)', html_content, re.DOTALL)
    if not overview_section:
        return market_overview
    
    content = overview_section.group()
    
    # 提取各种板型
    board_types = ['一字板', 'T字板', '天地板', '地天板', '反包', '大面股', '大长腿']
    
    for board_type in board_types:
        # 查找该板型下的股票
        pattern = f'{board_type}.*?(?={"｜".join([bt for bt in board_types if bt != board_type])}|$)'
        section_match = re.search(pattern, content, re.DOTALL)
        
        if section_match:
            section_text = section_match.group()
            # 提取股票信息
            stock_matches = re.findall(r'([深沪科创])\s+\[([^\]]+)\]\(/gupiao-(\d+)\.html\)\s+\[\s*([-+]?\d+\.?\d*)\s*%\s*\]\s+\(([^)]+)\)', section_text)
            
            stocks = []
            for market, name, code, percent, concept in stock_matches:
                stocks.append({
                    'market': market,
                    'name': name.strip(),
                    'code': code,
                    'change_percent': percent,
                    'concept': concept.strip()
                })
            
            if stocks:
                market_overview[board_type] = stocks
    
    return market_overview

def extract_longhu_summary_fpb(html_content):
    """提取龙虎榜摘要数据"""
    longhu_summary = []
    
    # 查找龙虎榜部分
    longhu_section = re.search(r'游资龙虎榜摘要.*?(?=\[回到顶部\]|$)', html_content, re.DOTALL)
    if not longhu_section:
        return longhu_summary
    
    content = longhu_section.group()
    
    # 提取游资信息
    youzai_pattern = r'([^/\n]+?)\s+\[([^\]]+)\]\s+\[([^\]]+)\]\(/gupiao-(\d+)\.html\)\s+\(\s*([^)]+)\s*\)\s+买\s+([\d,]+)\s*万'
    matches = re.findall(youzai_pattern, content)
    
    for match in matches[:20]:  # 只取前20个
        youzai, market, name, code, concept, amount = match
        longhu_summary.append({
            'youzai': youzai.strip(),
            'market': market,
            'stock_name': name.strip(),
            'code': code,
            'concept': concept.strip(),
            'buy_amount': amount.replace(',', '')
        })
    
    return longhu_summary

def calculate_success_rate(ratio):
    """计算成功率"""
    if '/' in ratio:
        success, total = ratio.split('/')
        if int(total) > 0:
            return f"{int(success) / int(total) * 100:.1f}%"
    return "0%"

def print_fpb_data(data):
    """打印复盘宝数据"""
    print("\n" + "="*100)
    print("🚀 大板客连板雁阵图数据 - 复盘宝版本 (数据更完整)")
    print("="*100)
    
    if data.get('update_time'):
        print(f"📅 数据更新时间: {data['update_time']}")
    print()
    
    # 按级别显示股票
    level_order = ['5进6', '4进5', '2进3', '1进2', '首板']
    level_emojis = {
        '5进6': '🔥🔥🔥🔥🔥',
        '4进5': '🔥🔥🔥🔥',
        '2进3': '🔥🔥🔥',
        '1进2': '🔥🔥',
        '首板': '🔥'
    }
    
    total_stocks = 0
    total_success = 0
    total_fail = 0
    total_bomb = 0
    
    for level in level_order:
        stocks = data.get('stocks_by_level', {}).get(level, [])
        if stocks:
            print(f"{level_emojis.get(level, '🔥')} {level} 级别股票 ({len(stocks)}只):")
            print("-"*95)
            print(f"{'序号':<4} {'市场':<4} {'股票名称':<12} {'代码':<8} {'状态':<8} {'涨幅':<8} {'概念/行业':<30}")
            print("-"*95)
            
            level_success = level_fail = level_bomb = 0
            
            for i, stock in enumerate(stocks, 1):
                status_display = f"{stock['status_emoji']}{stock['status']}"
                percent_display = f"{stock['change_percent']}%"
                concept = stock.get('concept', '')[:28]
                
                print(f"{i:<4} {stock['market']:<4} {stock['name']:<12} {stock['code']:<8} {status_display:<8} {percent_display:<8} {concept:<30}")
                
                # 统计
                if stock['status'] == '成':
                    level_success += 1
                elif stock['status'] == '败':
                    level_fail += 1
                elif stock['status'] == '炸':
                    level_bomb += 1
            
            level_total = len(stocks)
            level_success_rate = (level_success / level_total * 100) if level_total > 0 else 0
            print(f"\n  📊 {level} 统计: 总计{level_total}只, 成功{level_success}只✅, 失败{level_fail}只❌, 炸板{level_bomb}只💥, 成功率{level_success_rate:.1f}%")
            print()
            
            total_stocks += level_total
            total_success += level_success
            total_fail += level_fail
            total_bomb += level_bomb
    
    # 概念统计
    if data.get('concept_stats'):
        print("🎯 热门概念板块统计:")
        print("-"*80)
        print(f"{'序号':<4} {'概念名称':<20} {'成功率':<10} {'股票数量':<8} {'代表股票':<30}")
        print("-"*80)
        
        for i, concept in enumerate(data['concept_stats'][:15], 1):
            stock_count = len(concept.get('stocks', []))
            representative_stocks = ', '.join([s['name'] for s in concept.get('stocks', [])[:3]])
            print(f"{i:<4} {concept['concept']:<20} {concept['success_rate']:<10} {stock_count:<8} {representative_stocks:<30}")
        print()
    
    # 市场总览
    if data.get('market_overview'):
        print("📈 市场总览 (特殊板型):")
        print("-"*70)
        for board_type, stocks in data['market_overview'].items():
            if stocks:
                stock_names = ', '.join([s['name'] for s in stocks[:5]])
                print(f"  {board_type}: {len(stocks)}只 - {stock_names}")
                if len(stocks) > 5:
                    print(f"    ... 还有{len(stocks) - 5}只")
        print()
    
    # 龙虎榜摘要
    if data.get('longhu_summary'):
        print("💰 龙虎榜游资动向 (前10名):")
        print("-"*80)
        print(f"{'游资':<15} {'股票':<12} {'概念':<15} {'买入金额(万)':<12}")
        print("-"*80)
        for longhu in data['longhu_summary'][:10]:
            print(f"{longhu['youzai']:<15} {longhu['stock_name']:<12} {longhu['concept']:<15} {longhu['buy_amount']:<12}")
        print()
    
    # 总体统计
    if total_stocks > 0:
        overall_success_rate = (total_success / total_stocks * 100)
        print("📊 整体市场情绪分析:")
        print("-"*70)
        print(f"  总股票数: {total_stocks}只")
        print(f"  成功(成): {total_success}只 ✅  失败(败): {total_fail}只 ❌  炸板(炸): {total_bomb}只 💥")
        print(f"  整体成功率: {overall_success_rate:.1f}%")
        
        if overall_success_rate >= 60:
            print("  🎉 市场情绪: 强势 - 连板氛围良好")
        elif overall_success_rate >= 40:
            print("  😐 市场情绪: 中性 - 谨慎观望")
        else:
            print("  😰 市场情绪: 弱势 - 避险为主")
    
    print("="*100)

def main():
    """主函数"""
    print("🔄 正在获取大板客复盘宝页面数据...")
    
    html_content = get_dabanke_fpb_data()
    if not html_content:
        return
    
    print("✅ 复盘宝数据获取成功，正在解析连板雁阵图...")
    
    # 提取数据
    data = extract_lianban_data_from_fpb(html_content)
    
    print("✅ 连板雁阵图数据解析完成")
    
    # 打印数据
    print_fpb_data(data)
    
    # 保存数据
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"dabanke_fpb_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"\n💾 完整数据已保存到: {filename}")
        print("📝 包含按级别分类的股票、概念统计、市场总览和龙虎榜数据")
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")

if __name__ == "__main__":
    main()
