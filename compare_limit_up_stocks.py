#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比涨停股票名单
"""

from market_info_sina import SinaMarketDataCollector

def compare_limit_up():
    """对比涨停股票名单"""
    
    # 同花顺显示的涨停股票名单（从图片中提取）
    tonghuashun_limit_up = [
        "华西证券", "华鑫股份", "苏宁易购", "亚太科技", "上海沿浦", "浙江建投",
        "华阳股份", "国联股份", "山东路桥", "北部湾港", "今世缘", "宁波建工",
        "中材节能", "华统股份", "中国海诚", "华西能源", "中钢天源", "华菱钢铁",
        "中国中冶", "华金资本", "华电国际", "华能国际", "华润电力", "华电重工",
        "华润三九", "华润双鹤", "华润微", "华润材料", "华润万东", "华润置地",
        "华夏银行", "华夏幸福", "华侨城A", "华泰证券", "华安证券", "华创阳安",
        "华林证券", "华西股份", "华贸物流", "华友钴业"
    ]
    
    collector = SinaMarketDataCollector()
    
    print("=== 对比涨停股票名单 ===\n")
    
    # 获取我们统计的涨停股票详情
    limit_details = collector.get_limit_up_down_details()
    
    if not limit_details or not limit_details.get('limit_up_details'):
        print("未获取到涨停股票详情")
        return
    
    our_limit_up = limit_details['limit_up_details']
    
    # 过滤掉B股
    our_limit_up_no_b = []
    for stock in our_limit_up:
        if not collector.is_b_share(stock['code']):
            our_limit_up_no_b.append(stock)
    
    print(f"同花顺涨停股票: {len(tonghuashun_limit_up)}只")
    print(f"我们统计涨停股票: {len(our_limit_up)}只")
    print(f"我们统计涨停股票(不含B股): {len(our_limit_up_no_b)}只")
    print(f"差异: {len(our_limit_up_no_b) - len(tonghuashun_limit_up)}只")
    
    print(f"\n=== 我们统计的涨停股票(前50只) ===")
    for i, stock in enumerate(our_limit_up_no_b[:50], 1):
        name = stock['name']
        code = stock['code']
        change_pct = stock['change_pct']
        price = stock['current_price']
        is_st = collector.is_st_stock(name)
        
        # 检查是否在同花顺名单中
        in_tonghuashun = name in tonghuashun_limit_up
        status = "✅" if in_tonghuashun else "❌"
        st_mark = "[ST]" if is_st else ""
        
        print(f"  {i:2d}. {status} {name}{st_mark} ({code}) 涨幅: {change_pct:.2f}% 价格: {price:.2f}元")
    
    # 分析差异
    print(f"\n=== 差异分析 ===")
    
    # 找出我们多统计的股票
    our_names = [stock['name'] for stock in our_limit_up_no_b]
    extra_stocks = []
    missing_stocks = []
    
    for stock in our_limit_up_no_b:
        if stock['name'] not in tonghuashun_limit_up:
            extra_stocks.append(stock)
    
    for name in tonghuashun_limit_up:
        if name not in our_names:
            missing_stocks.append(name)
    
    print(f"\n我们多统计的股票 ({len(extra_stocks)}只):")
    for i, stock in enumerate(extra_stocks[:20], 1):
        name = stock['name']
        code = stock['code']
        change_pct = stock['change_pct']
        is_st = collector.is_st_stock(name)
        st_mark = "[ST]" if is_st else ""
        
        # 分析可能的原因
        reason = ""
        if is_st:
            reason = "ST股票"
        elif change_pct < 9.9:
            reason = f"涨幅不足10% ({change_pct:.2f}%)"
        elif "华" not in name:
            reason = "非华字头股票"
        
        print(f"  {i:2d}. {name}{st_mark} ({code}) 涨幅: {change_pct:.2f}% - {reason}")
    
    print(f"\n同花顺有但我们缺少的股票 ({len(missing_stocks)}只):")
    for i, name in enumerate(missing_stocks[:20], 1):
        print(f"  {i:2d}. {name}")
    
    # 分析涨停标准
    print(f"\n=== 涨停标准分析 ===")
    
    # 统计不同涨幅范围的股票
    ranges = {
        "9.8%-9.9%": 0,
        "9.9%-10.0%": 0,
        "10.0%-10.1%": 0,
        ">10.1%": 0,
        "ST 4.8%-4.9%": 0,
        "ST 4.9%-5.0%": 0,
        "ST >5.0%": 0
    }
    
    for stock in our_limit_up_no_b:
        change_pct = stock['change_pct']
        is_st = collector.is_st_stock(stock['name'])
        
        if is_st:
            if 4.8 <= change_pct < 4.9:
                ranges["ST 4.8%-4.9%"] += 1
            elif 4.9 <= change_pct < 5.0:
                ranges["ST 4.9%-5.0%"] += 1
            elif change_pct >= 5.0:
                ranges["ST >5.0%"] += 1
        else:
            if 9.8 <= change_pct < 9.9:
                ranges["9.8%-9.9%"] += 1
            elif 9.9 <= change_pct < 10.0:
                ranges["9.9%-10.0%"] += 1
            elif 10.0 <= change_pct < 10.1:
                ranges["10.0%-10.1%"] += 1
            elif change_pct >= 10.1:
                ranges[">10.1%"] += 1
    
    print("涨幅分布:")
    for range_name, count in ranges.items():
        print(f"  {range_name}: {count}只")
    
    # 建议
    print(f"\n=== 建议调整 ===")
    print("可能的差异原因:")
    print("1. 我们的涨停标准过于宽松 (≥9.8%)")
    print("2. 同花顺可能使用更严格的标准 (≥9.95%或≥10.0%)")
    print("3. 同花顺可能排除了某些特殊股票")
    print("4. 数据获取时间差异")
    
    print("\n建议:")
    print("1. 调整涨停标准为 ≥9.95%")
    print("2. 排除ST股票中涨幅过低的")
    print("3. 检查是否有重复统计")

if __name__ == "__main__":
    compare_limit_up()
