#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用问财API获取涨跌停数据
"""

import pywencai
import pandas as pd
from datetime import datetime

def get_limit_stocks(cookie=None):
    """获取涨跌停股票数据"""

    print("=== 问财涨跌停数据获取 ===\n")

    try:
        # 获取跌停股票
        print("📉 正在获取跌停股票...")
        limit_down = pywencai.get(query='今天跌停', sort_order='asc', cookie=cookie)

        if isinstance(limit_down, pd.DataFrame) and not limit_down.empty:
            print(f"跌停股票数量: {len(limit_down)}只")
            print("跌停股票详情:")

            # 显示跌停股票信息
            for i, (_, row) in enumerate(limit_down.iterrows(), 1):
                # 尝试获取股票信息
                name = ""
                code = ""
                change = ""

                # 查找包含股票信息的列
                for col in limit_down.columns:
                    if '股票简称' in col or '名称' in col:
                        name = row[col]
                    elif '股票代码' in col or '代码' in col:
                        code = row[col]
                    elif '涨跌幅' in col or '涨幅' in col:
                        change = row[col]

                print(f"  {i}. {name} ({code}) 跌幅: {change}")
        else:
            print("跌停股票数量: 0只")

        print()

        # 获取涨停股票
        print("📈 正在获取涨停股票...")
        limit_up = pywencai.get(query='今天涨停', sort_order='asc', cookie=cookie)

        if isinstance(limit_up, pd.DataFrame) and not limit_up.empty:
            print(f"涨停股票数量: {len(limit_up)}只")
            print("涨停股票详情(前10只):")

            # 显示前10只涨停股票
            for i, (_, row) in enumerate(limit_up.head(10).iterrows(), 1):
                name = ""
                code = ""
                change = ""

                for col in limit_up.columns:
                    if '股票简称' in col or '名称' in col:
                        name = row[col]
                    elif '股票代码' in col or '代码' in col:
                        code = row[col]
                    elif '涨跌幅' in col or '涨幅' in col:
                        change = row[col]

                print(f"  {i}. {name} ({code}) 涨幅: {change}")
        else:
            print("涨停股票数量: 0只")

        # 保存数据
        today = datetime.now().strftime('%Y-%m-%d')

        if isinstance(limit_up, pd.DataFrame) or isinstance(limit_down, pd.DataFrame):
            try:
                with pd.ExcelWriter(f'wencai_limit_data_{today}.xlsx') as writer:
                    if isinstance(limit_up, pd.DataFrame):
                        limit_up.to_excel(writer, sheet_name='涨停股票', index=False)
                    if isinstance(limit_down, pd.DataFrame):
                        limit_down.to_excel(writer, sheet_name='跌停股票', index=False)
                print(f"\n数据已保存到: wencai_limit_data_{today}.xlsx")
            except Exception as e:
                print(f"保存数据失败: {e}")

        # 返回统计结果
        return {
            'limit_up_count': len(limit_up) if isinstance(limit_up, pd.DataFrame) else 0,
            'limit_down_count': len(limit_down) if isinstance(limit_down, pd.DataFrame) else 0,
            'limit_up_data': limit_up,
            'limit_down_data': limit_down,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    except Exception as e:
        print(f"❌ 数据获取失败: {e}")
        print("\n可能的原因:")
        print("1. Cookie无效或过期")
        print("2. 网络连接问题")
        print("3. 问财API限制")
        print("\n获取Cookie的方法:")
        print("1. 访问 https://www.iwencai.com/")
        print("2. 登录账号")
        print("3. 按F12打开开发者工具")
        print("4. 在Network标签中找到请求，复制Cookie")
        return None

if __name__ == "__main__":
    # 设置Cookie（需要从浏览器获取）
    cookie = 'xxx'  # 请替换为实际的cookie

    if cookie == 'xxx':
        print("⚠️  请先设置有效的问财Cookie")
        print("尝试使用默认设置获取数据...\n")

    result = get_limit_stocks(cookie=cookie)

    if result:
        print(f"\n=== 汇总 ===")
        print(f"数据获取时间: {result['timestamp']}")
        print(f"涨停股票: {result['limit_up_count']}只")
        print(f"跌停股票: {result['limit_down_count']}只")
        print(f"数据来源: 问财(同花顺)")
    else:
        print("\n数据获取失败，请检查Cookie设置")
# res = pywencai.get(query='昨日深市沪市创业板科创板北证成交额',query_type='zhishu')
# print(res)
# res = pywencai.get(query='创业板成交额',query_type='zhishu')
# print('创业板成交额:',res)